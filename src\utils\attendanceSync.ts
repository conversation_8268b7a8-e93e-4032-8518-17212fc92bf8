/**
 * Attendance synchronization utilities for cross-device check-ins
 */

import { type Attendee, type Session } from './localStorage';

// Key for storing pending check-ins that need to be synced
const PENDING_CHECKINS_KEY = 'pending_checkins';

export interface PendingCheckIn {
  sessionId: string;
  attendee: Attendee;
  timestamp: Date;
  deviceId: string;
}

/**
 * Generate a unique device ID for tracking check-ins
 */
export const getDeviceId = (): string => {
  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = 'device_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
};

/**
 * Store a check-in that needs to be synced back to the organizer
 */
export const storePendingCheckIn = (sessionId: string, attendee: Attendee): void => {
  const pendingCheckIn: PendingCheckIn = {
    sessionId,
    attendee,
    timestamp: new Date(),
    deviceId: getDeviceId()
  };

  const existing = getPendingCheckIns();
  existing.push(pendingCheckIn);
  
  localStorage.setItem(PENDING_CHECKINS_KEY, JSON.stringify(existing));
  
  // Also try to sync immediately if possible
  syncPendingCheckIns();
};

/**
 * Get all pending check-ins from localStorage
 */
export const getPendingCheckIns = (): PendingCheckIn[] => {
  try {
    const stored = localStorage.getItem(PENDING_CHECKINS_KEY);
    if (!stored) return [];
    
    const checkIns = JSON.parse(stored);
    return checkIns.map((checkIn: any) => ({
      ...checkIn,
      timestamp: new Date(checkIn.timestamp),
      attendee: {
        ...checkIn.attendee,
        timestamp: new Date(checkIn.attendee.timestamp)
      }
    }));
  } catch (error) {
    console.error('Error loading pending check-ins:', error);
    return [];
  }
};

/**
 * Clear pending check-ins after successful sync
 */
export const clearPendingCheckIns = (): void => {
  localStorage.removeItem(PENDING_CHECKINS_KEY);
};

/**
 * Generate a sync URL that contains pending check-ins
 */
export const generateSyncUrl = (baseUrl: string): string => {
  const pendingCheckIns = getPendingCheckIns();
  if (pendingCheckIns.length === 0) return baseUrl;
  
  const syncData = encodeURIComponent(JSON.stringify(pendingCheckIns));
  const separator = baseUrl.includes('?') ? '&' : '?';
  
  return `${baseUrl}${separator}syncData=${syncData}`;
};

/**
 * Process sync data from URL parameters
 */
export const processSyncData = (): PendingCheckIn[] => {
  const urlParams = new URLSearchParams(window.location.search);
  const syncData = urlParams.get('syncData');
  
  if (!syncData) return [];
  
  try {
    const checkIns = JSON.parse(decodeURIComponent(syncData));
    return checkIns.map((checkIn: any) => ({
      ...checkIn,
      timestamp: new Date(checkIn.timestamp),
      attendee: {
        ...checkIn.attendee,
        timestamp: new Date(checkIn.attendee.timestamp)
      }
    }));
  } catch (error) {
    console.error('Error processing sync data:', error);
    return [];
  }
};

/**
 * Attempt to sync pending check-ins (placeholder for future enhancement)
 */
export const syncPendingCheckIns = (): void => {
  // For now, this is a placeholder
  // In a real implementation, this could use WebRTC, WebSockets, or other P2P methods
  console.log('Sync attempt - pending check-ins:', getPendingCheckIns().length);
};

/**
 * Create a shareable link for manual sync
 */
export const createSyncLink = (baseUrl: string): string => {
  const pendingCheckIns = getPendingCheckIns();
  if (pendingCheckIns.length === 0) return '';
  
  return generateSyncUrl(`${baseUrl}/sync`);
};
