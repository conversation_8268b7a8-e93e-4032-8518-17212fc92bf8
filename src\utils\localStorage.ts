
export interface Session {
  id: string;
  name: string;
  createdAt: Date;
  attendees: Attendee[];
}

export interface Attendee {
  email: string;
  name: string;
  timestamp: Date;
}

const SESSIONS_KEY = 'qr_attendance_sessions';

export const saveSession = (session: Session): void => {
  const sessions = getSessions();
  const existingIndex = sessions.findIndex(s => s.id === session.id);
  
  if (existingIndex >= 0) {
    sessions[existingIndex] = session;
  } else {
    sessions.push(session);
  }
  
  localStorage.setItem(SESSIONS_KEY, JSON.stringify(sessions));
};

export const getSessions = (): Session[] => {
  try {
    const stored = localStorage.getItem(SESSIONS_KEY);
    if (!stored) return [];
    
    const sessions = JSON.parse(stored);
    // Convert date strings back to Date objects
    return sessions.map((session: any) => ({
      ...session,
      createdAt: new Date(session.createdAt),
      attendees: session.attendees.map((attendee: any) => ({
        ...attendee,
        timestamp: new Date(attendee.timestamp)
      }))
    }));
  } catch (error) {
    console.error('Error loading sessions from localStorage:', error);
    return [];
  }
};

export const getSession = (id: string): Session | null => {
  const sessions = getSessions();
  const localSession = sessions.find(session => session.id === id);

  // If session found locally, return it
  if (localSession) {
    return localSession;
  }

  // If not found locally, try to get from URL parameters (for cross-device access)
  const urlParams = new URLSearchParams(window.location.search);
  const sessionData = urlParams.get('sessionData');

  if (sessionData) {
    try {
      const decodedSession = JSON.parse(decodeURIComponent(sessionData));
      // Convert date strings back to Date objects
      return {
        ...decodedSession,
        createdAt: new Date(decodedSession.createdAt),
        attendees: decodedSession.attendees.map((attendee: any) => ({
          ...attendee,
          timestamp: new Date(attendee.timestamp)
        }))
      };
    } catch (error) {
      console.error('Error parsing session data from URL:', error);
    }
  }

  return null;
};

export const addAttendeeToSession = (sessionId: string, attendee: Attendee): Session | null => {
  const sessions = getSessions();
  const sessionIndex = sessions.findIndex(s => s.id === sessionId);

  if (sessionIndex >= 0) {
    sessions[sessionIndex].attendees.push(attendee);
    localStorage.setItem(SESSIONS_KEY, JSON.stringify(sessions));
    return sessions[sessionIndex];
  }

  // If session not found locally, try to handle cross-device attendance
  // For now, we'll create a temporary session record for cross-device check-ins
  const urlParams = new URLSearchParams(window.location.search);
  const sessionData = urlParams.get('sessionData');

  if (sessionData) {
    try {
      const decodedSession = JSON.parse(decodeURIComponent(sessionData));
      const updatedSession = {
        ...decodedSession,
        createdAt: new Date(decodedSession.createdAt),
        attendees: [
          ...decodedSession.attendees.map((a: any) => ({
            ...a,
            timestamp: new Date(a.timestamp)
          })),
          attendee
        ]
      };

      // Store the updated session locally for this device
      const localSessions = getSessions();
      localSessions.push(updatedSession);
      localStorage.setItem(SESSIONS_KEY, JSON.stringify(localSessions));

      return updatedSession;
    } catch (error) {
      console.error('Error updating cross-device session:', error);
    }
  }

  return null;
};
