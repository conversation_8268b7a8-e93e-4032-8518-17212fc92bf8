import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Wifi, Smartphone, Monitor, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const NetworkTest = () => {
  const [networkInfo, setNetworkInfo] = useState({
    currentUrl: '',
    hostname: '',
    port: '',
    protocol: '',
    userAgent: '',
    isMobile: false,
    timestamp: new Date()
  });

  useEffect(() => {
    const updateNetworkInfo = () => {
      const url = new URL(window.location.href);
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      
      setNetworkInfo({
        currentUrl: window.location.href,
        hostname: url.hostname,
        port: url.port || '80',
        protocol: url.protocol,
        userAgent: navigator.userAgent,
        isMobile,
        timestamp: new Date()
      });
    };

    updateNetworkInfo();
    const interval = setInterval(updateNetworkInfo, 5000);
    return () => clearInterval(interval);
  }, []);

  const testConnectivity = async () => {
    try {
      const response = await fetch('/');
      return response.ok;
    } catch (error) {
      return false;
    }
  };

  const [connectivityTest, setConnectivityTest] = useState<boolean | null>(null);

  const runConnectivityTest = async () => {
    setConnectivityTest(null);
    const result = await testConnectivity();
    setConnectivityTest(result);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wifi className="h-6 w-6" />
              Network Connectivity Test
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="font-semibold flex items-center gap-2">
                  {networkInfo.isMobile ? <Smartphone className="h-4 w-4" /> : <Monitor className="h-4 w-4" />}
                  Device Type
                </h3>
                <Badge variant={networkInfo.isMobile ? "default" : "secondary"}>
                  {networkInfo.isMobile ? "Mobile Device" : "Desktop/Laptop"}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <h3 className="font-semibold">Connection Status</h3>
                <div className="flex gap-2">
                  <Button onClick={runConnectivityTest} size="sm">
                    Test Connection
                  </Button>
                  {connectivityTest !== null && (
                    <Badge variant={connectivityTest ? "default" : "destructive"}>
                      {connectivityTest ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Connected
                        </>
                      ) : (
                        <>
                          <XCircle className="h-3 w-3 mr-1" />
                          Failed
                        </>
                      )}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Network Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Current URL:</span>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => copyToClipboard(networkInfo.currentUrl)}
                  >
                    Copy
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mt-1 break-all">{networkInfo.currentUrl}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <span className="font-medium text-blue-900">Hostname:</span>
                  <p className="text-blue-700">{networkInfo.hostname}</p>
                </div>
                
                <div className="p-3 bg-green-50 rounded-lg">
                  <span className="font-medium text-green-900">Port:</span>
                  <p className="text-green-700">{networkInfo.port}</p>
                </div>
                
                <div className="p-3 bg-purple-50 rounded-lg">
                  <span className="font-medium text-purple-900">Protocol:</span>
                  <p className="text-purple-700">{networkInfo.protocol}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Mobile Access Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {networkInfo.hostname === 'localhost' ? (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-yellow-900">Localhost Detected</h4>
                    <p className="text-yellow-800 text-sm mt-1">
                      You're accessing via localhost. Mobile devices won't be able to connect.
                    </p>
                    <p className="text-yellow-700 text-sm mt-2">
                      <strong>Solution:</strong> Access the server using your computer's IP address instead.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-green-900">Network Access Available</h4>
                    <p className="text-green-800 text-sm mt-1">
                      Mobile devices on the same network should be able to access this server.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-3">
              <h4 className="font-semibold">For Mobile Access:</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
                <li>Ensure your mobile device is on the same WiFi network</li>
                <li>Create an attendance session on this computer</li>
                <li>Generate the QR code</li>
                <li>Scan the QR code with your mobile device</li>
                <li>The mobile device should open the attendance form</li>
              </ol>
            </div>

            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Expected Mobile URL:</h4>
              <p className="text-blue-700 text-sm break-all">
                {networkInfo.hostname !== 'localhost' 
                  ? `${networkInfo.protocol}//${networkInfo.hostname}:${networkInfo.port}/attendance/[session-id]`
                  : 'http://[YOUR-IP-ADDRESS]:8080/attendance/[session-id]'
                }
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Troubleshooting</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div>
                <h4 className="font-semibold">If mobile can't connect:</h4>
                <ul className="list-disc list-inside space-y-1 text-gray-700 mt-1">
                  <li>Check that both devices are on the same WiFi network</li>
                  <li>Try disabling Windows Firewall temporarily</li>
                  <li>Make sure no VPN is running on either device</li>
                  <li>Try accessing the IP address directly in mobile browser first</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold">Server Information:</h4>
                <p className="text-gray-700">Server should be running on: <code>http://***********:8080</code></p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Device Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600 break-all">{networkInfo.userAgent}</p>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Last updated: {networkInfo.timestamp.toLocaleString()}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default NetworkTest;
