/**
 * Utility functions for network-related operations
 */

// Default network IP - can be overridden via localStorage
const DEFAULT_NETWORK_IP = '***********';

/**
 * Get the configured network IP (from localStorage or default)
 */
const getConfiguredNetworkIP = (): string => {
  return localStorage.getItem('network_ip') || DEFAULT_NETWORK_IP;
};

/**
 * Get the network URL for the current application
 * This function returns the appropriate URL for network access
 */
export const getNetworkUrl = (): Promise<string> => {
  return new Promise((resolve) => {
    const hostname = window.location.hostname;
    const port = window.location.port;
    const protocol = window.location.protocol;

    // If already accessing via network IP, use current origin
    if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
      resolve(window.location.origin);
      return;
    }

    // Use the configured network IP
    resolve(`${protocol}//${getConfiguredNetworkIP()}:${port}`);
  });
};

/**
 * Get network URL synchronously
 */
export const getNetworkUrlSync = (): string => {
  const hostname = window.location.hostname;
  const port = window.location.port;
  const protocol = window.location.protocol;

  // If already accessing via network IP, use current origin
  if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
    return window.location.origin;
  }

  // Use the configured network IP
  return `${protocol}//${getConfiguredNetworkIP()}:${port}`;
};
