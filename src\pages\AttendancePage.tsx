
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import EnhancedAttendanceScanner from '@/components/EnhancedAttendanceScanner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { QrCode, AlertCircle } from 'lucide-react';
import { getSession, addAttendeeToSession, type Session, type Attendee } from '@/utils/localStorage';

const AttendancePage = () => {
  const { sessionId } = useParams();
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (sessionId) {
      // Load session from localStorage
      const loadedSession = getSession(sessionId);
      console.log('Loading session:', sessionId, 'Found:', loadedSession);

      // Debug: Log URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      console.log('URL parameters:', Object.fromEntries(urlParams.entries()));

      setSession(loadedSession);
    }
    setLoading(false);
  }, [sessionId]);

  const handleAttendeeAdd = (email: string, name: string) => {
    if (session) {
      const newAttendee: Attendee = {
        email,
        name: name || email.split('@')[0],
        timestamp: new Date(),
      };
      
      const updatedSession = addAttendeeToSession(session.id, newAttendee);
      
      if (updatedSession) {
        setSession(updatedSession);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center">
        <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
          <CardContent className="flex flex-col items-center justify-center py-16">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mb-4" />
            <p className="text-gray-600">Loading session...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <QrCode className="h-10 w-10 text-blue-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">QR Attendance Check-in</h1>
          </div>
          <p className="text-lg text-gray-600">
            Welcome! Please check in to the session below.
          </p>
        </div>

        {/* Main Content */}
        <div className="max-w-md mx-auto">
          {session ? (
            <EnhancedAttendanceScanner onAttendeeAdd={handleAttendeeAdd} session={session} />
          ) : (
            <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="flex flex-col items-center justify-center py-16">
                <AlertCircle className="h-16 w-16 text-red-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-700 mb-2">Session Not Found</h3>
                <p className="text-gray-500 text-center mb-4">
                  This attendance session doesn't exist or may have been deleted.
                </p>

                {/* Debug Information */}
                <div className="bg-gray-50 p-3 rounded-lg text-xs text-gray-600 max-w-md">
                  <p><strong>Debug Info:</strong></p>
                  <p>Session ID: {sessionId}</p>
                  <p>URL: {window.location.href}</p>
                  <p>Has sessionData: {new URLSearchParams(window.location.search).has('sessionData') ? 'Yes' : 'No'}</p>
                  <p className="mt-2 text-blue-600">
                    💡 If you scanned a QR code, make sure the session was created on the same network.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Back to Home Link */}
        <div className="text-center mt-8">
          <a 
            href="/" 
            className="text-blue-600 hover:text-blue-800 underline"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  );
};

export default AttendancePage;
