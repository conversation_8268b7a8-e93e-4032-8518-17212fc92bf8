import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Users, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react';
import { processSyncData, type PendingCheckIn } from '@/utils/attendanceSync';
import { addAttendeeToSession, getSessions } from '@/utils/localStorage';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

const SyncPage = () => {
  const [syncedCheckIns, setSyncedCheckIns] = useState<PendingCheckIn[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedCount, setProcessedCount] = useState(0);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    // Process sync data from URL
    const checkIns = processSyncData();
    setSyncedCheckIns(checkIns);
    
    if (checkIns.length > 0) {
      processSyncedData(checkIns);
    }
  }, []);

  const processSyncedData = async (checkIns: PendingCheckIn[]) => {
    setIsProcessing(true);
    let processed = 0;
    
    for (const checkIn of checkIns) {
      try {
        // Add the attendee to the session
        const result = addAttendeeToSession(checkIn.sessionId, checkIn.attendee);
        if (result) {
          processed++;
        }
      } catch (error) {
        console.error('Error processing check-in:', error);
      }
    }
    
    setProcessedCount(processed);
    setIsProcessing(false);
    
    if (processed > 0) {
      toast({
        title: "Sync Successful! 🎉",
        description: `${processed} check-in(s) have been added to your sessions.`,
      });
    }
  };

  const goHome = () => {
    navigate('/');
  };

  if (syncedCheckIns.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <RefreshCw className="h-12 w-12 text-blue-600 mr-3" />
              <h1 className="text-3xl font-bold text-gray-900">Attendance Sync</h1>
            </div>
          </div>

          <div className="max-w-md mx-auto">
            <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="flex flex-col items-center justify-center py-16">
                <AlertCircle className="h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-700 mb-2">No Sync Data</h3>
                <p className="text-gray-500 text-center mb-4">
                  This page is used to sync attendance data from mobile devices.
                </p>
                <p className="text-sm text-gray-400 text-center">
                  Mobile users will be provided with a sync link after checking in.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="text-center mt-8">
            <Button onClick={goHome} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <RefreshCw className="h-12 w-12 text-green-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">Syncing Attendance Data</h1>
          </div>
          <p className="text-lg text-gray-600">
            Processing check-ins from mobile devices...
          </p>
        </div>

        <div className="max-w-2xl mx-auto space-y-6">
          {/* Sync Status */}
          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {isProcessing ? (
                  <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />
                ) : (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                )}
                Sync Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <h3 className="font-semibold text-blue-900 text-2xl">{syncedCheckIns.length}</h3>
                  <p className="text-sm text-blue-700">Check-ins Received</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <h3 className="font-semibold text-green-900 text-2xl">{processedCount}</h3>
                  <p className="text-sm text-green-700">Successfully Processed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Check-ins List */}
          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                Synced Check-ins
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {syncedCheckIns.map((checkIn, index) => (
                  <div 
                    key={index}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{checkIn.attendee.name}</h4>
                        {checkIn.attendee.email && (
                          <p className="text-sm text-gray-500">{checkIn.attendee.email}</p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">
                        {checkIn.attendee.timestamp.toLocaleTimeString()}
                      </p>
                      <p className="text-xs text-gray-400">
                        Session: {checkIn.sessionId}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {!isProcessing && (
            <div className="text-center">
              <Button onClick={goHome} className="bg-green-600 hover:bg-green-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                Complete - Go to Dashboard
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SyncPage;
