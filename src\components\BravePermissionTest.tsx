import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Mic, Shield, Lock, AlertTriangle, CheckCircle } from 'lucide-react';

const BravePermissionTest = () => {
  const [isBrave, setIsBrave] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState('unknown');
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Detect Brave browser
    const detectBrave = async () => {
      const brave = navigator.brave && navigator.brave.isBrave;
      const userAgent = navigator.userAgent;
      const isBraveUA = userAgent.includes('Brave') || userAgent.includes('brave');
      
      setIsBrave(brave || isBraveUA);
      
      // Check initial permission status
      if (navigator.permissions) {
        try {
          const result = await navigator.permissions.query({ name: 'microphone' });
          setPermissionStatus(result.state);
        } catch (error) {
          console.log('Permission query failed:', error);
        }
      }
    };
    
    detectBrave();
  }, []);

  const addTestResult = (test, success, message) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testMicrophonePermission = async () => {
    setIsLoading(true);
    setTestResults([]);

    try {
      addTestResult('Browser Detection', true, `Brave: ${isBrave ? 'Yes' : 'No'}`);

      // Test 1: Check MediaDevices API availability
      addTestResult('Navigator Check', !!navigator, `Navigator object: ${!!navigator ? 'Available' : 'Missing'}`);
      addTestResult('MediaDevices Check', !!navigator.mediaDevices, `MediaDevices: ${!!navigator.mediaDevices ? 'Available' : 'Missing'}`);

      if (navigator.mediaDevices) {
        addTestResult('getUserMedia Check', !!navigator.mediaDevices.getUserMedia, `getUserMedia: ${!!navigator.mediaDevices.getUserMedia ? 'Available' : 'Missing'}`);
      }

      // Enhanced compatibility check for Brave
      let getUserMedia = null;
      let methodUsed = 'none';

      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        getUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
        methodUsed = 'modern';
      } else if (navigator.getUserMedia) {
        getUserMedia = (constraints) => {
          return new Promise((resolve, reject) => {
            navigator.getUserMedia(constraints, resolve, reject);
          });
        };
        methodUsed = 'legacy';
      } else if (navigator.webkitGetUserMedia) {
        getUserMedia = (constraints) => {
          return new Promise((resolve, reject) => {
            navigator.webkitGetUserMedia(constraints, resolve, reject);
          });
        };
        methodUsed = 'webkit';
      } else if (navigator.mozGetUserMedia) {
        getUserMedia = (constraints) => {
          return new Promise((resolve, reject) => {
            navigator.mozGetUserMedia(constraints, resolve, reject);
          });
        };
        methodUsed = 'mozilla';
      }

      if (!getUserMedia) {
        addTestResult('getUserMedia Support', false, 'No getUserMedia method available');
        if (isBrave) {
          addTestResult('Brave Analysis', false, 'MediaDevices API is completely blocked by Brave. Check brave://settings/content/microphone');
        }
        return;
      }

      addTestResult('getUserMedia Support', true, `Using ${methodUsed} getUserMedia method`);

      // Test 2: Try minimal constraints
      try {
        addTestResult('Permission Request', true, 'Requesting microphone access...');
        const stream = await getUserMedia({ audio: true });
        addTestResult('Minimal Constraints', true, 'Permission granted with minimal constraints');

        // Stop the stream
        if (stream && stream.getTracks) {
          stream.getTracks().forEach(track => track.stop());
        }
        setPermissionStatus('granted');

        // Test 3: Try with full constraints
        try {
          const fullStream = await getUserMedia({
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true
            }
          });
          addTestResult('Full Constraints', true, 'Permission granted with full constraints');
          if (fullStream && fullStream.getTracks) {
            fullStream.getTracks().forEach(track => track.stop());
          }
        } catch (fullError) {
          addTestResult('Full Constraints', false, `Failed: ${fullError.name || 'Unknown error'}`);
        }

      } catch (error) {
        addTestResult('Permission Request', false, `${error.name || 'Unknown'}: ${error.message || 'Unknown error'}`);
        setPermissionStatus('denied');

        if (isBrave) {
          if (error.message && error.message.includes('undefined')) {
            addTestResult('Brave Analysis', false, 'MediaDevices API is undefined - completely blocked by Brave Shields');
          } else if (error.name === 'NotAllowedError') {
            addTestResult('Brave Analysis', false, 'Permission dialog may not have appeared. Check Brave Shields.');
          } else {
            addTestResult('Brave Analysis', false, `Brave-specific error: ${error.name || 'Unknown'}`);
          }
        }
      }

    } catch (error) {
      addTestResult('General Error', false, error.message || 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const openBraveSettings = () => {
    window.open('brave://settings/content/microphone', '_blank');
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-6 w-6" />
          Brave Browser Microphone Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Browser Detection */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            <span>Browser Detection:</span>
          </div>
          <Badge variant={isBrave ? 'destructive' : 'default'}>
            {isBrave ? 'Brave Browser' : 'Other Browser'}
          </Badge>
        </div>

        {/* Permission Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Mic className="h-5 w-5" />
            <span>Permission Status:</span>
          </div>
          <Badge variant={
            permissionStatus === 'granted' ? 'default' : 
            permissionStatus === 'denied' ? 'destructive' : 
            'secondary'
          }>
            {permissionStatus}
          </Badge>
        </div>

        {/* Test Button */}
        <Button
          onClick={testMicrophonePermission}
          disabled={isLoading}
          className="w-full h-12"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Testing...
            </>
          ) : (
            <>
              <Mic className="h-5 w-5 mr-2" />
              Test Microphone Permission
            </>
          )}
        </Button>

        {/* Brave-Specific Instructions */}
        {isBrave && (
          <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <h3 className="font-semibold text-orange-800 mb-2 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Brave Browser Instructions
            </h3>
            <div className="text-sm text-orange-700 space-y-2">
              <p><strong>If the permission dialog doesn't appear:</strong></p>
              <div className="space-y-1 ml-4">
                <p>1. Click the lock icon (🔒) next to the address bar</p>
                <p>2. Find "Microphone" and change to "Allow"</p>
                <p>3. Refresh the page and test again</p>
              </div>
              <div className="flex gap-2 mt-3">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={openBraveSettings}
                  className="text-orange-700 border-orange-300"
                >
                  <Shield className="h-4 w-4 mr-1" />
                  Open Brave Settings
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="text-orange-700 border-orange-300"
                >
                  🔄 Refresh Page
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Test Results:</h3>
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    result.success 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {result.success ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="font-medium">{result.test}</span>
                    </div>
                    <span className="text-xs text-gray-500">{result.timestamp}</span>
                  </div>
                  <p className={`text-sm mt-1 ${
                    result.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {result.message}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Manual Instructions */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">Manual Permission Steps:</h3>
          <div className="text-sm text-blue-700 space-y-1">
            <p>1. Look for the lock icon (🔒) or shield icon (🛡️) in the address bar</p>
            <p>2. Click it to open site permissions</p>
            <p>3. Find "Microphone" and set to "Allow"</p>
            <p>4. Refresh this page and test again</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BravePermissionTest;
