import { Card, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Users, Clock, Download, Mail, User, FileText } from 'lucide-react';
import mgbLogo from '@/images/MGB LOGO.png';
import bagongPilipinasLogo from '@/images/Bagong Pilipinas Logo.png';

// Type definitions
interface Attendee {
  name: string;
  email?: string;
  timestamp: Date;
}

interface Session {
  id: string;
  name: string;
  createdAt: Date;
  attendees: Attendee[];
}

interface AttendanceRecord {
  name: string;
  email?: string;
  timestamp: Date;
}
import { Document, Packer, Paragraph, Table, TableCell, TableRow, TextRun, AlignmentType, WidthType, ImageRun, Media } from 'docx';

interface AttendanceListProps {
  session: Session | null;
  attendanceRecords: AttendanceRecord[];
}

const AttendanceList = ({ session, attendanceRecords }: AttendanceListProps) => {
  const exportToCSV = () => {
    if (!session || session.attendees.length === 0) return;

    // Government-style attendance format headers
    const headers = ['No.', 'NAME', 'SIGNATURE'];

    // Generate rows in government format
    const rows = session.attendees.map((attendee: Attendee, index: number) => [
      String(index + 1), // Sequential number
      attendee.name.toUpperCase(), // Name in uppercase like government format
      '' // Empty signature field for manual signing
    ]);

    // Create CSV content with proper escaping
    const csvContent = [headers, ...rows]
      .map(row => row.map((field: string) => `"${String(field).replace(/"/g, '""')}"`).join(','))
      .join('\n');

    // Add BOM for proper Excel compatibility
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${session.name.replace(/\s+/g, '-')}-Attendance-${new Date().toISOString().split('T')[0]}.csv`);
    link.click();
    URL.revokeObjectURL(url);
  };

  const exportTemplate = () => {
    // Export government-style attendance template
    const headers = ['No.', 'NAME', 'SIGNATURE'];

    // Create sample rows to show format (like government form)
    const sampleRows = [
      ['1', 'SAMPLE NAME 1', ''],
      ['2', 'SAMPLE NAME 2', ''],
      ['3', 'SAMPLE NAME 3', ''],
      ['4', '', ''],
      ['5', '', ''],
      ['6', '', ''],
      ['7', '', ''],
      ['8', '', ''],
      ['9', '', ''],
      ['10', '', ''],
      ['11', '', ''],
      ['12', '', ''],
      ['13', '', ''],
      ['14', '', ''],
      ['15', '', ''],
      ['16', '', ''],
      ['17', '', ''],
      ['18', '', ''],
      ['19', '', ''],
      ['20', '', '']
    ];

    const csvContent = [headers, ...sampleRows]
      .map(row => row.map((field: string) => `"${String(field).replace(/"/g, '""')}"`).join(','))
      .join('\n');

    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `Attendance-Template-${new Date().toISOString().split('T')[0]}.csv`);
    link.click();
    URL.revokeObjectURL(url);
  };

  const exportToWord = async () => {
    if (!session || session.attendees.length === 0) return;

    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    try {
      // Fetch and convert images to buffers
      const mgbLogoResponse = await fetch(mgbLogo);
      const mgbLogoBuffer = await mgbLogoResponse.arrayBuffer();

      const bagongPilipinasResponse = await fetch(bagongPilipinasLogo);
      const bagongPilipinasBuffer = await bagongPilipinasResponse.arrayBuffer();

      const doc = new Document({
        sections: [{
          properties: {
            page: {
              margin: {
                top: 720,    // 0.5 inch
                right: 720,  // 0.5 inch
                bottom: 720, // 0.5 inch
                left: 720,   // 0.5 inch
              },
            },
          },
          children: [
            // Header with Logos and Text
            new Table({
              width: {
                size: 100,
                type: WidthType.PERCENTAGE,
              },
              borders: {
                top: { style: "none", size: 0 },
                bottom: { style: "none", size: 0 },
                left: { style: "none", size: 0 },
                right: { style: "none", size: 0 },
                insideHorizontal: { style: "none", size: 0 },
                insideVertical: { style: "none", size: 0 },
              },
              rows: [
                new TableRow({
                  children: [
                    new TableCell({
                      children: [new Paragraph({
                        children: [
                          new ImageRun({
                            data: new Uint8Array(mgbLogoBuffer),
                            transformation: {
                              width: 100,
                              height: 100,
                            },
                            type: "png",
                          }),
                        ],
                        alignment: AlignmentType.LEFT,
                      })],
                      width: { size: 20, type: WidthType.PERCENTAGE },
                      verticalAlign: "center",
                    }),
                    new TableCell({
                      children: [
                        new Paragraph({
                          children: [
                            new TextRun({
                              text: "Republic of the Philippines",
                              size: 24,
                              font: "Arial",
                              bold: false,
                            }),
                          ],
                          alignment: AlignmentType.CENTER,
                          spacing: { after: 100 },
                        }),
                        new Paragraph({
                          children: [
                            new TextRun({
                              text: "Department of Environment and Natural Resources",
                              size: 20,
                              font: "Arial",
                              bold: false,
                            }),
                          ],
                          alignment: AlignmentType.CENTER,
                          spacing: { after: 100 },
                        }),
                        new Paragraph({
                          children: [
                            new TextRun({
                              text: "MINES AND GEOSCIENCES BUREAU",
                              bold: true,
                              size: 24,
                              color: "0066CC",
                              font: "Arial",
                            }),
                          ],
                          alignment: AlignmentType.CENTER,
                          spacing: { after: 100 },
                        }),
                        new Paragraph({
                          children: [
                            new TextRun({
                              text: "Regional Office No. II",
                              size: 18,
                              font: "Arial",
                              bold: true,
                              color: "0066CC",
                            }),
                          ],
                          alignment: AlignmentType.CENTER,
                          spacing: { after: 50 },
                        }),
                      ],
                      width: { size: 60, type: WidthType.PERCENTAGE },
                      verticalAlign: "center",
                    }),
                    new TableCell({
                      children: [new Paragraph({
                        children: [
                          new ImageRun({
                            data: new Uint8Array(bagongPilipinasBuffer),
                            transformation: {
                              width: 100,
                              height: 100,
                            },
                            type: "png",
                          }),
                        ],
                        alignment: AlignmentType.RIGHT,
                      })],
                      width: { size: 20, type: WidthType.PERCENTAGE },
                      verticalAlign: "center",
                    }),
                  ],
                }),
              ],
            }),

            // Address Section
            new Paragraph({
              children: [
                new TextRun({
                  text: "No. 18 Dalana na Pagayaya Corner Matunong, Regional Government Center",
                  size: 16,
                  font: "Arial",
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 50 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "Carig Sur, Tuguegarao City",
                  size: 16,
                  font: "Arial",
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 50 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "Regional Office No. II",
                  size: 14,
                  font: "Arial",
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 50 },
            }),
            new Paragraph({
              children: [
                new TextRun({
                  text: "E-mail: <EMAIL>/<EMAIL>",
                  size: 14,
                  font: "Arial",
                }),
              ],
              alignment: AlignmentType.CENTER,
              spacing: { after: 400 },
            }),
          new Paragraph({
            children: [
              new TextRun({
                text: "MINES AND GEOSCIENCES BUREAU",
                bold: true,
                size: 22,
                color: "0000FF",
                font: "Arial",
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 100 },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "Regional Office No. II",
                size: 18,
                font: "Arial",
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 50 },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "No. 18 Dalana na Pagayaya Corner Matunong, Regional Government Center",
                size: 16,
                font: "Arial",
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 50 },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "Carig Sur, Tuguegarao City",
                size: 16,
                font: "Arial",
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 50 },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "Regional Office No. II",
                size: 14,
                font: "Arial",
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 50 },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "E-mail: <EMAIL>/<EMAIL>",
                size: 14,
                font: "Arial",
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 400 },
          }),

          // Title Section
          new Paragraph({
            children: [
              new TextRun({
                text: `${session.name.toUpperCase()} ATTENDANCE`,
                bold: true,
                size: 24,
                font: "Arial",
                underline: {},
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 200 },
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: currentDate,
                size: 18,
                font: "Arial",
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 400 },
          }),

          // Attendance Table
          new Table({
            width: {
              size: 100,
              type: WidthType.PERCENTAGE,
            },
            borders: {
              top: { style: "single", size: 1 },
              bottom: { style: "single", size: 1 },
              left: { style: "single", size: 1 },
              right: { style: "single", size: 1 },
              insideHorizontal: { style: "single", size: 1 },
              insideVertical: { style: "single", size: 1 },
            },
            rows: [
              // Header Row
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({
                      children: [new TextRun({
                        text: "No.",
                        bold: true,
                        size: 20,
                        font: "Arial"
                      })],
                      alignment: AlignmentType.CENTER,
                    })],
                    width: { size: 10, type: WidthType.PERCENTAGE },
                    shading: { fill: "E6E6E6" },
                  }),
                  new TableCell({
                    children: [new Paragraph({
                      children: [new TextRun({
                        text: "NAME",
                        bold: true,
                        size: 20,
                        font: "Arial"
                      })],
                      alignment: AlignmentType.CENTER,
                    })],
                    width: { size: 60, type: WidthType.PERCENTAGE },
                    shading: { fill: "E6E6E6" },
                  }),
                  new TableCell({
                    children: [new Paragraph({
                      children: [new TextRun({
                        text: "TIMESTAMP",
                        bold: true,
                        size: 20,
                        font: "Arial"
                      })],
                      alignment: AlignmentType.CENTER,
                    })],
                    width: { size: 30, type: WidthType.PERCENTAGE },
                    shading: { fill: "E6E6E6" },
                  }),
                ],
              }),
              // Data Rows
              ...session.attendees.map((attendee: Attendee, index: number) =>
                new TableRow({
                  children: [
                    new TableCell({
                      children: [new Paragraph({
                        children: [new TextRun({
                          text: String(index + 1),
                          size: 18,
                          font: "Arial"
                        })],
                        alignment: AlignmentType.CENTER,
                      })],
                    }),
                    new TableCell({
                      children: [new Paragraph({
                        children: [new TextRun({
                          text: attendee.name.toUpperCase(),
                          size: 18,
                          font: "Arial"
                        })]
                      })],
                    }),
                    new TableCell({
                      children: [new Paragraph({
                        children: [new TextRun({
                          text: attendee.timestamp.toLocaleString('en-US', {
                            month: '2-digit',
                            day: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: true
                          }),
                          size: 16,
                          font: "Arial"
                        })]
                      })],
                    }),
                  ],
                })
              ),
            ],
          }),
        ],
      }],
    });

      const blob = await Packer.toBlob(doc);
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `Official-${session.name.replace(/\s+/g, '-')}-Attendance-${currentDate.replace(/\s+/g, '-')}.docx`);
      link.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error generating Word document:', error);
    }
  };

  if (!session) {
    return (
      <Card className="card-elevated border-0">
        <CardContent className="flex flex-col items-center justify-center py-20">
          <div className="w-24 h-24 bg-gradient-to-br from-neutral-200 to-neutral-300 rounded-3xl flex items-center justify-center mb-6 shadow-soft">
            <Users className="h-12 w-12 text-neutral-500" />
          </div>
          <h3 className="text-heading text-neutral-700 mb-4">No Active Session</h3>
          <p className="text-body text-neutral-500 text-center max-w-md leading-relaxed">
            Create a new attendance session to start tracking attendees.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Enhanced Session Overview */}
      <Card className="card-elevated border-0">
        <CardHeader className="pb-6">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3 text-heading text-gray-800">
              <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center">
                <Users className="h-5 w-5 text-white" />
              </div>
              Attendance Overview
            </CardTitle>
            <div className="flex gap-3">
              {session.attendees.length > 0 && (
                <>
                  <Button
                    onClick={exportToCSV}
                    variant="outline"
                    className="border-green-300 text-green-700 hover:bg-green-50 rounded-xl font-medium"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export CSV
                  </Button>
                  <Button
                    onClick={exportToWord}
                    variant="outline"
                    className="border-blue-300 text-blue-700 hover:bg-blue-50 rounded-xl font-medium"
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Export Word
                  </Button>
                </>
              )}
              <Button
                onClick={exportTemplate}
                variant="outline"
                className="border-purple-300 text-purple-700 hover:bg-purple-50 rounded-xl font-medium"
              >
                <Download className="h-4 w-4 mr-2" />
                CSV Template
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-2xl text-center border border-blue-200 shadow-soft">
              <h3 className="font-bold text-blue-900 text-lg mb-1">{session.name}</h3>
              <p className="text-caption text-blue-700">Session Name</p>
            </div>
            <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-2xl text-center border border-green-200 shadow-soft">
              <h3 className="font-bold text-green-900 text-3xl mb-1">{session.attendees.length}</h3>
              <p className="text-caption text-green-700">Total Attendees</p>
            </div>
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-2xl text-center border border-purple-200 shadow-soft">
              <h3 className="font-bold text-purple-900 text-lg mb-1">{session.createdAt.toLocaleDateString()}</h3>
              <p className="text-caption text-purple-700">Session Date</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Attendees List */}
      <Card className="card-elevated border-0">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-3 text-heading text-gray-800">
            <div className="w-10 h-10 bg-gradient-secondary rounded-xl flex items-center justify-center">
              <User className="h-5 w-5 text-white" />
            </div>
            Attendee List
          </CardTitle>
        </CardHeader>
        <CardContent>
          {session.attendees.length === 0 ? (
            <div className="text-center py-16">
              <div className="w-20 h-20 bg-gradient-to-br from-gray-200 to-gray-300 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-soft">
                <Users className="h-10 w-10 text-gray-500" />
              </div>
              <h3 className="text-subheading text-gray-600 mb-3">No Attendees Yet</h3>
              <p className="text-body text-gray-500">Share the QR code to start collecting attendance!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {session.attendees.map((attendee: Attendee, index: number) => (
                <div
                  key={index}
                  className="card-interactive p-6 hover:shadow-medium"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-primary rounded-2xl flex items-center justify-center shadow-soft">
                        <User className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 text-lg">{attendee.name}</h4>
                        {attendee.email && (
                          <div className="flex items-center text-body text-gray-600 mt-1">
                            <Mail className="h-4 w-4 mr-2" />
                            {attendee.email}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge className="bg-gradient-success text-white mb-2 px-3 py-1 rounded-xl font-medium">
                        ✓ Checked In
                      </Badge>
                      <div className="flex items-center text-caption text-gray-500">
                        <Clock className="h-4 w-4 mr-2" />
                        {attendee.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Recent Check-ins */}
      {attendanceRecords.length > 0 && (
        <Card className="card-elevated border-0">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center gap-3 text-heading text-gray-800">
              <div className="w-10 h-10 bg-gradient-success rounded-xl flex items-center justify-center">
                <Clock className="h-5 w-5 text-white" />
              </div>
              Recent Check-ins
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {attendanceRecords.slice(0, 5).map((record: AttendanceRecord, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-xl border border-green-200 shadow-soft"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-3 h-3 bg-gradient-success rounded-full shadow-soft"></div>
                    <div>
                      <span className="font-semibold text-green-900 text-base">{record.name}</span>
                      {record.email && (
                        <p className="text-green-700 text-sm mt-1">{record.email}</p>
                      )}
                    </div>
                  </div>
                  <span className="text-green-600 text-sm font-medium">
                    {record.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AttendanceList;
