# Network Access Setup for QR Attendance System

## Overview
This guide helps you set up network access so mobile devices can scan QR codes and check in to attendance sessions.

## Quick Setup

### 1. Find Your Network IP Address

**Windows:**
```cmd
ipconfig
```
Look for "IPv4 Address" under your WiFi adapter (usually starts with 192.168 or 10.0)

**Mac/Linux:**
```bash
ifconfig
# or
ip addr show
```
Look for your WiFi interface's inet address

### 2. Update Network Configuration

1. Open the attendance system in your browser: `http://localhost:8080`
2. Go to the **Network** tab
3. Click **Edit** next to the Network IP Address
4. Enter your computer's IP address (found in step 1)
5. Click **Save**

### 3. Test Network Access

1. Create a new attendance session
2. Go to the **QR Code** tab
3. The QR code should now contain your network IP (e.g., `http://*************:8080/attend/...`)
4. Try accessing the URL from your mobile device's browser

## Troubleshooting

### "Session Not Found" when scanning QR code

This is the most common issue. Here's how to fix it:

**Root Cause:** The session data is stored locally on your computer, but mobile devices don't have access to it.

**Solution:** The system now embeds session data directly in QR codes. If you're still getting this error:

1. **Recreate the session:** Delete the old session and create a new one
2. **Generate new QR code:** Go to QR Code tab after creating the new session
3. **Check the URL:** The QR code URL should be very long and contain `sessionData=` parameter
4. **Test locally first:** Try the QR code URL in your computer's browser before testing on mobile

### Mobile devices can't access the system

**Check WiFi Connection:**
- Ensure your computer and mobile devices are on the same WiFi network
- Guest networks often block device-to-device communication
- Corporate networks may have device isolation enabled

**Check Firewall:**
- Windows: Allow port 8080 through Windows Firewall
- Mac: System Preferences > Security & Privacy > Firewall > Firewall Options > Allow port 8080
- Linux: `sudo ufw allow 8080`

**Check Network IP:**
- Make sure the IP in the Network tab matches your computer's actual IP
- IP addresses can change when you reconnect to WiFi
- Try accessing `http://YOUR_IP:8080` directly from mobile browser

### QR codes still show localhost

- Make sure you've updated the Network IP in the Network tab
- Refresh the page after changing the network IP
- Clear your browser cache if needed
- Create a new session after updating the network IP

### Debug Information

When you see "Session Not Found", check the debug information shown on the error page:
- **Session ID:** Should match the one in your QR code
- **Has sessionData:** Should show "Yes" if scanned from QR code
- **URL:** Should contain your network IP, not localhost

## Network IP Examples

- **Home WiFi:** Usually `192.168.1.x` or `192.168.0.x`
- **Office WiFi:** Often `10.0.x.x` or `172.16.x.x`
- **Mobile Hotspot:** Typically `192.168.43.x`

## Security Notes

- The system only works on your local network
- No data is sent to external servers
- All attendance data stays on your computer
- The system is only accessible while your computer is running

## Automatic Cross-Device Sync

### How It Works (Super Simple!)

The system now uses **automatic real-time synchronization**:

1. **📱 Mobile User:** Scans QR code and enters their name/email
2. **⚡ Instant Sync:** Data automatically appears on organizer's device
3. **🎉 Done!** No manual sharing or links needed

### Step-by-Step Process

**For Mobile Users:**
1. Scan QR code with your phone
2. Enter your name and/or email
3. Tap "Check In to Session"
4. See success page - you're done!

**For Organizers:**
1. Create session and share QR code as usual
2. Watch your screen - new check-ins appear automatically
3. Get instant notifications when someone checks in
4. All attendance data syncs in real-time

### Technical Details

**Real-time Sync Methods:**
- **Primary:** BroadcastChannel API for instant communication
- **Fallback:** localStorage events for cross-browser compatibility
- **Network:** Same WiFi network required for sync to work

**What You'll See:**
- Organizer gets toast notifications: "New Check-in Received! 🎉"
- Attendance list updates automatically
- No manual intervention needed

## Default Configuration

The system comes pre-configured with IP `***********` which matches the Vite development server output. Update this to match your actual network IP for mobile access.
