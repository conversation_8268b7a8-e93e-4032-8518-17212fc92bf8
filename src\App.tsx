
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import AttendancePage from "./pages/AttendancePage";
import MobileAttendancePage from "./pages/MobileAttendancePage";
import CheckInSuccess from "./pages/CheckInSuccess";
import SyncPage from "./pages/SyncPage";
import SyncTest from "./pages/SyncTest";
import VoiceTestComponent from "./components/VoiceTestComponent";
import BravePermissionTest from "./components/BravePermissionTest";
import DebugTest from "./pages/DebugTest";
import NetworkTest from "./pages/NetworkTest";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/attend/:sessionId" element={<AttendancePage />} />
          <Route path="/mobile-attend/:sessionId" element={<MobileAttendancePage />} />
          <Route path="/success/:sessionId" element={<CheckInSuccess />} />
          <Route path="/sync" element={<SyncPage />} />
          <Route path="/test-sync" element={<SyncTest />} />
          <Route path="/voice-test" element={<VoiceTestComponent />} />
          <Route path="/brave-test" element={<BravePermissionTest />} />
          <Route path="/debug" element={<DebugTest />} />
          <Route path="/network-test" element={<NetworkTest />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
