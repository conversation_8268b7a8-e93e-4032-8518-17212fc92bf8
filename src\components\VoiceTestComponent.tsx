import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Volume2, AlertCircle, CheckCircle } from 'lucide-react';

const VoiceTestComponent = () => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isSupported, setIsSupported] = useState(false);
  const [error, setError] = useState('');
  const [recognition, setRecognition] = useState(null);
  const [browserInfo, setBrowserInfo] = useState('');
  const [permissionStatus, setPermissionStatus] = useState('unknown');

  useEffect(() => {
    // Detect browser and speech recognition support
    const userAgent = navigator.userAgent;
    setBrowserInfo(userAgent);

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const supported = !!SpeechRecognition;
    setIsSupported(supported);

    // Check microphone permission
    if (navigator.permissions) {
      navigator.permissions.query({ name: 'microphone' }).then((result) => {
        setPermissionStatus(result.state);
        result.onchange = () => {
          setPermissionStatus(result.state);
        };
      });
    }

    console.log('Browser:', userAgent);
    console.log('Speech Recognition supported:', supported);
    console.log('SpeechRecognition object:', SpeechRecognition);
  }, []);

  const startListening = async () => {
    if (!isSupported) {
      setError('Speech recognition not supported in this browser');
      return;
    }

    try {
      // Request microphone permission first
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          stream.getTracks().forEach(track => track.stop()); // Stop the stream, we just needed permission
          console.log('Microphone permission granted');
        } catch (permError) {
          setError('Microphone permission denied');
          return;
        }
      }

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const newRecognition = new SpeechRecognition();

      // Configure recognition
      newRecognition.continuous = false;
      newRecognition.interimResults = false;
      newRecognition.lang = 'en-US';
      newRecognition.maxAlternatives = 3;

      // Event handlers
      newRecognition.onstart = () => {
        console.log('Speech recognition started');
        setIsListening(true);
        setError('');
        setTranscript('');
      };

      newRecognition.onend = () => {
        console.log('Speech recognition ended');
        setIsListening(false);
      };

      newRecognition.onresult = (event) => {
        console.log('Speech recognition result:', event);
        let finalTranscript = '';
        
        for (let i = 0; i < event.results.length; i++) {
          if (event.results[i].isFinal) {
            finalTranscript += event.results[i][0].transcript;
          }
        }
        
        if (finalTranscript) {
          console.log('Final transcript:', finalTranscript);
          setTranscript(finalTranscript);
          setError('');
        }
      };

      newRecognition.onerror = (event) => {
        console.error('Speech recognition error:', event);
        setIsListening(false);
        
        let errorMessage = '';
        switch (event.error) {
          case 'not-allowed':
            errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings.';
            break;
          case 'no-speech':
            errorMessage = 'No speech detected. Please try speaking again.';
            break;
          case 'audio-capture':
            errorMessage = 'No microphone found. Please check your microphone connection.';
            break;
          case 'network':
            errorMessage = 'Network error occurred. Please check your internet connection.';
            break;
          case 'service-not-allowed':
            errorMessage = 'Speech recognition service not allowed. Please enable it in browser settings.';
            break;
          default:
            errorMessage = `Speech recognition error: ${event.error}`;
        }
        setError(errorMessage);
      };

      newRecognition.onnomatch = () => {
        console.log('No speech match found');
        setError('Could not understand speech. Please try again.');
      };

      setRecognition(newRecognition);
      newRecognition.start();

    } catch (error) {
      console.error('Error starting speech recognition:', error);
      setError(`Failed to start speech recognition: ${error.message}`);
      setIsListening(false);
    }
  };

  const stopListening = () => {
    if (recognition) {
      recognition.stop();
    }
    setIsListening(false);
  };

  const testMicrophone = async () => {
    try {
      setPermissionStatus('requesting');

      // Detect Brave browser
      const isBrave = navigator.brave && navigator.brave.isBrave;
      const userAgent = navigator.userAgent;
      const isLikelyBrave = isBrave || userAgent.includes('Brave') || userAgent.includes('brave');

      console.log('Browser detection for microphone test:', {
        isBrave,
        isLikelyBrave,
        userAgent
      });

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      console.log('Microphone test successful');
      setError('Microphone test successful! ✅ Permission granted.');
      setPermissionStatus('granted');

      // Stop the stream
      stream.getTracks().forEach(track => track.stop());
    } catch (error) {
      console.error('Microphone test failed:', error);
      setPermissionStatus('denied');

      // Enhanced error handling for Brave browser
      const isBrave = navigator.brave && navigator.brave.isBrave;
      const userAgent = navigator.userAgent;
      const isLikelyBrave = isBrave || userAgent.includes('Brave') || userAgent.includes('brave');

      let errorMessage = 'Microphone test failed: ';

      if (isLikelyBrave) {
        errorMessage = '🛡️ Brave Browser: ';
        if (error.name === 'NotAllowedError') {
          errorMessage += 'Microphone blocked by Brave Shields. Click the shield icon (🛡️) in address bar → Allow microphone → Refresh page.';
        } else {
          errorMessage += `${error.name}: ${error.message}. Check Brave settings.`;
        }
      } else if (error.name === 'NotAllowedError') {
        errorMessage += 'Permission denied. Please click "Allow" when prompted.';
      } else if (error.name === 'NotFoundError') {
        errorMessage += 'No microphone found. Please connect a microphone.';
      } else {
        errorMessage += error.message;
      }
      setError(errorMessage);
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Volume2 className="h-6 w-6" />
          Voice Recognition Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Browser Support Info */}
        <div className="space-y-3">
          <h3 className="font-semibold">Browser Support</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-center gap-2">
              {isSupported ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <span className="text-sm">
                Speech Recognition: {isSupported ? 'Supported' : 'Not Supported'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={permissionStatus === 'granted' ? 'default' : 'destructive'}>
                Microphone: {permissionStatus}
              </Badge>
            </div>
          </div>
          <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
            <strong>Browser:</strong> {browserInfo.includes('Chrome') ? 'Chrome' : 
                                      browserInfo.includes('Safari') ? 'Safari' : 
                                      browserInfo.includes('Firefox') ? 'Firefox' : 
                                      browserInfo.includes('Edge') ? 'Edge' : 'Unknown'}
          </div>
        </div>

        {/* Test Controls */}
        <div className="space-y-3">
          <h3 className="font-semibold">Test Controls</h3>
          <div className="flex gap-3">
            <Button
              onClick={testMicrophone}
              variant="outline"
              className="flex-1"
            >
              Test Microphone
            </Button>
            <Button
              onClick={isListening ? stopListening : startListening}
              disabled={!isSupported}
              className={`flex-1 ${isListening ? 'bg-red-500 hover:bg-red-600' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
            >
              {isListening ? (
                <>
                  <MicOff className="h-4 w-4 mr-2" />
                  Stop Listening
                </>
              ) : (
                <>
                  <Mic className="h-4 w-4 mr-2" />
                  Start Listening
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Status Display */}
        <div className="space-y-3">
          <h3 className="font-semibold">Status</h3>
          
          {isListening && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-blue-800 font-medium">Listening... Speak now!</span>
              </div>
            </div>
          )}

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800 font-medium">Error:</p>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {transcript && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-800 font-medium">Transcript:</p>
              <p className="text-green-700">"{transcript}"</p>
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="space-y-3">
          <h3 className="font-semibold">Instructions</h3>
          <div className="text-sm text-gray-600 space-y-2">
            <p>1. Click "Test Microphone" to verify microphone access</p>
            <p>2. Click "Start Listening" to begin voice recognition</p>
            <p>3. Speak clearly and wait for the transcript to appear</p>
            <p>4. If it doesn't work, check browser console for detailed errors</p>
          </div>
        </div>

        {/* Browser-Specific Troubleshooting */}
        <div className="space-y-3">
          <h3 className="font-semibold">Troubleshooting</h3>

          {/* Brave Browser Specific */}
          {(() => {
            const isBrave = navigator.brave && navigator.brave.isBrave;
            const userAgent = navigator.userAgent;
            const isLikelyBrave = isBrave || userAgent.includes('Brave') || userAgent.includes('brave');

            if (isLikelyBrave) {
              return (
                <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <h4 className="font-semibold text-orange-800 mb-2">🛡️ Brave Browser Detected</h4>
                  <div className="text-sm text-orange-700 space-y-2">
                    <p><strong>If microphone is not working:</strong></p>
                    <div className="space-y-1 ml-4">
                      <p>1. Look for the shield icon (🛡️) in the address bar</p>
                      <p>2. Click it and select "Allow microphone"</p>
                      <p>3. Refresh the page and try again</p>
                      <p>4. Alternative: Go to <code className="bg-orange-100 px-1 rounded">brave://settings/content/microphone</code></p>
                      <p>5. Add this site to "Allow" list</p>
                    </div>
                    <p className="mt-2"><strong>Note:</strong> Brave's privacy features may block microphone access by default.</p>
                  </div>
                </div>
              );
            }

            return null;
          })()}

          <div className="text-sm text-gray-600 space-y-1">
            <p>• <strong>Chrome/Edge:</strong> Best support, should work reliably</p>
            <p>• <strong>Safari:</strong> Good support on iOS/macOS</p>
            <p>• <strong>Firefox:</strong> Limited support, may not work</p>
            <p>• <strong>Brave:</strong> Requires manual permission in Shields settings</p>
            <p>• <strong>HTTPS Required:</strong> Voice recognition requires secure connection</p>
            <p>• <strong>Microphone Permission:</strong> Must be granted in browser</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VoiceTestComponent;
