import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { saveSession, getSessions, addAttendeeToSession, type Session, type Attendee } from '@/utils/localStorage';
import { useToast } from '@/hooks/use-toast';

const DebugTest = () => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [testName, setTestName] = useState('Test User');
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [selectedSession, setSelectedSession] = useState<Session | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = () => {
    const allSessions = getSessions();
    setSessions(allSessions);
    if (allSessions.length > 0) {
      setSelectedSession(allSessions[0]);
    }
  };

  const createTestSession = () => {
    const newSession: Session = {
      id: Date.now().toString(),
      name: `Test Session ${new Date().toLocaleTimeString()}`,
      createdAt: new Date(),
      attendees: []
    };
    
    saveSession(newSession);
    loadSessions();
    setSelectedSession(newSession);
    
    toast({
      title: "Test Session Created",
      description: `Created session: ${newSession.name}`,
    });
  };

  const testCheckIn = () => {
    if (!selectedSession) {
      toast({
        title: "No Session Selected",
        description: "Please create or select a session first",
        variant: "destructive"
      });
      return;
    }

    const newAttendee: Attendee = {
      email: testEmail,
      name: testName,
      timestamp: new Date(),
    };

    const updatedSession = addAttendeeToSession(selectedSession.id, newAttendee);
    
    if (updatedSession) {
      setSelectedSession(updatedSession);
      loadSessions();
      toast({
        title: "Check-in Successful! 🎉",
        description: `${testName} has been checked in`,
      });
    } else {
      toast({
        title: "Check-in Failed",
        description: "Could not add attendee to session",
        variant: "destructive"
      });
    }
  };

  const clearAllSessions = () => {
    localStorage.removeItem('qr_attendance_sessions');
    loadSessions();
    setSelectedSession(null);
    toast({
      title: "Sessions Cleared",
      description: "All sessions have been removed",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Debug Test Page</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button onClick={createTestSession} className="w-full">
                Create Test Session
              </Button>
              <Button onClick={clearAllSessions} variant="destructive" className="w-full">
                Clear All Sessions
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test Check-in</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="testName">Test Name</Label>
              <Input
                id="testName"
                value={testName}
                onChange={(e) => setTestName(e.target.value)}
                placeholder="Enter test name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="testEmail">Test Email</Label>
              <Input
                id="testEmail"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="Enter test email"
              />
            </div>
            <Button onClick={testCheckIn} className="w-full">
              Test Check-in
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Current Sessions ({sessions.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {sessions.length === 0 ? (
              <p className="text-gray-500">No sessions found</p>
            ) : (
              <div className="space-y-4">
                {sessions.map((session) => (
                  <div
                    key={session.id}
                    className={`p-4 border rounded-lg cursor-pointer ${
                      selectedSession?.id === session.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}
                    onClick={() => setSelectedSession(session)}
                  >
                    <h3 className="font-semibold">{session.name}</h3>
                    <p className="text-sm text-gray-600">
                      Created: {session.createdAt.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600">
                      Attendees: {session.attendees.length}
                    </p>
                    {session.attendees.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs font-medium text-gray-700">Recent attendees:</p>
                        <ul className="text-xs text-gray-600">
                          {session.attendees.slice(-3).map((attendee, index) => (
                            <li key={index}>
                              {attendee.name} ({attendee.email}) - {attendee.timestamp.toLocaleTimeString()}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Selected Session Details</CardTitle>
          </CardHeader>
          <CardContent>
            {selectedSession ? (
              <div>
                <h3 className="font-semibold">{selectedSession.name}</h3>
                <p className="text-sm text-gray-600">ID: {selectedSession.id}</p>
                <p className="text-sm text-gray-600">
                  Created: {selectedSession.createdAt.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">
                  Total Attendees: {selectedSession.attendees.length}
                </p>
              </div>
            ) : (
              <p className="text-gray-500">No session selected</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DebugTest;
