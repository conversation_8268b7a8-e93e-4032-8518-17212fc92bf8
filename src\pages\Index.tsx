
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import QRCodeGenerator from '@/components/QRCodeGenerator';
import EnhancedAttendanceScanner from '@/components/EnhancedAttendanceScanner';
import AttendanceList from '@/components/AttendanceList';
import NetworkConfig from '@/components/NetworkConfig';
import { Users, QrCode, Calendar, Settings } from 'lucide-react';
import { saveSession, getSessions, addAttendeeToSession, type Session, type Attendee } from '@/utils/localStorage';
import { initializeRealtimeSync, cleanupRealtimeSync, type SyncMessage } from '@/utils/realtimeSync';
import { useToast } from '@/hooks/use-toast';

const Index = () => {
  const [sessionName, setSessionName] = useState('');
  const [currentSession, setCurrentSession] = useState<Session | null>(null);
  const [attendanceRecords, setAttendanceRecords] = useState<Attendee[]>([]);
  const { toast } = useToast();

  // Load the most recent session on component mount
  useEffect(() => {
    // Initialize real-time sync
    initializeRealtimeSync((message: SyncMessage) => {
      if (message.type === 'NEW_CHECKIN' && message.attendee) {
        toast({
          title: "New Check-in Received! 🎉",
          description: `${message.attendee.name} has checked in from mobile device.`,
        });

        // Refresh the current session to show the new attendee
        refreshCurrentSession();
      }
    });

    // Load initial session data
    loadLatestSession();

    // Listen for custom storage events (fallback sync method)
    const handleAttendanceSync = (event: CustomEvent) => {
      const message = event.detail as SyncMessage;
      if (message.type === 'NEW_CHECKIN' && message.attendee) {
        toast({
          title: "New Check-in Received! 🎉",
          description: `${message.attendee.name} has checked in from mobile device.`,
        });
        refreshCurrentSession();
      }
    };

    window.addEventListener('attendanceSync', handleAttendanceSync as EventListener);

    // Cleanup on unmount
    return () => {
      cleanupRealtimeSync();
      window.removeEventListener('attendanceSync', handleAttendanceSync as EventListener);
    };
  }, []);

  const loadLatestSession = () => {
    const sessions = getSessions();
    if (sessions.length > 0) {
      // Load the most recently created session
      const latestSession = sessions.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0];
      setCurrentSession(latestSession);
      setAttendanceRecords(latestSession.attendees);
    }
  };

  const refreshCurrentSession = () => {
    if (currentSession) {
      const sessions = getSessions();
      const updatedSession = sessions.find(s => s.id === currentSession.id);
      if (updatedSession) {
        setCurrentSession(updatedSession);
        setAttendanceRecords(updatedSession.attendees);
      }
    }
  };

  const createSession = () => {
    if (sessionName.trim()) {
      const newSession: Session = {
        id: Date.now().toString(),
        name: sessionName,
        createdAt: new Date(),
        attendees: []
      };
      
      saveSession(newSession);
      setCurrentSession(newSession);
      setSessionName('');
      setAttendanceRecords([]);
    }
  };

  const addAttendee = (email: string, name: string) => {
    if (currentSession) {
      const newAttendee: Attendee = {
        email,
        name: name || email.split('@')[0],
        timestamp: new Date(),
      };
      
      const updatedSession = addAttendeeToSession(currentSession.id, newAttendee);
      
      if (updatedSession) {
        setCurrentSession(updatedSession);
        setAttendanceRecords(updatedSession.attendees);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
      <div className="container mx-auto px-6 py-12">
        {/* Enhanced Header */}
        <div className="text-center mb-16 animate-fade-in">
          <div className="flex items-center justify-center mb-6">
            <div className="relative mr-6">
              <div className="w-20 h-20 bg-gradient-primary rounded-3xl flex items-center justify-center shadow-strong">
                <QrCode className="h-10 w-10 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-success rounded-full flex items-center justify-center shadow-medium">
                <Users className="h-4 w-4 text-white" />
              </div>
            </div>
            <h1 className="text-display text-gradient-primary font-bold">
              AttendEase
            </h1>
          </div>
          <p className="text-subheading text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Smart QR code attendance tracking with real-time check-ins and seamless mobile support
          </p>
          <div className="mt-6 flex items-center justify-center gap-4 text-caption">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Real-time sync</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Mobile optimized</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>Voice enabled</span>
            </div>
          </div>
        </div>

        {/* Enhanced Main Content */}
        <div className="max-w-7xl mx-auto animate-slide-up">
          <Tabs defaultValue="create" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-12 h-16 bg-white/60 backdrop-blur-xl border border-gray-200/50 rounded-2xl p-2 shadow-soft">
              <TabsTrigger
                value="create"
                className="flex items-center gap-3 h-12 rounded-xl font-medium text-sm data-[state=active]:bg-gradient-primary data-[state=active]:text-white data-[state=active]:shadow-medium transition-all duration-300"
              >
                <Calendar className="h-4 w-4" />
                <span className="hidden sm:inline">Create Session</span>
                <span className="sm:hidden">Create</span>
              </TabsTrigger>
              <TabsTrigger
                value="scan"
                className="flex items-center gap-3 h-12 rounded-xl font-medium text-sm data-[state=active]:bg-gradient-primary data-[state=active]:text-white data-[state=active]:shadow-medium transition-all duration-300"
              >
                <QrCode className="h-4 w-4" />
                <span className="hidden sm:inline">QR Code</span>
                <span className="sm:hidden">QR</span>
              </TabsTrigger>
              <TabsTrigger
                value="attendance"
                className="flex items-center gap-3 h-12 rounded-xl font-medium text-sm data-[state=active]:bg-gradient-primary data-[state=active]:text-white data-[state=active]:shadow-medium transition-all duration-300"
              >
                <Users className="h-4 w-4" />
                <span className="hidden sm:inline">Attendance</span>
                <span className="sm:hidden">List</span>
              </TabsTrigger>
              <TabsTrigger
                value="settings"
                className="flex items-center gap-3 h-12 rounded-xl font-medium text-sm data-[state=active]:bg-gradient-primary data-[state=active]:text-white data-[state=active]:shadow-medium transition-all duration-300"
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Network</span>
                <span className="sm:hidden">Net</span>
              </TabsTrigger>
            </TabsList>

            {/* Enhanced Create Session Tab */}
            <TabsContent value="create" className="animate-scale-in">
              <Card className="card-elevated border-0">
                <CardHeader className="text-center pb-8">
                  <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-medium">
                    <Calendar className="h-8 w-8 text-white" />
                  </div>
                  <CardTitle className="text-heading text-gray-800">Create New Attendance Session</CardTitle>
                  <CardDescription className="text-body text-gray-600 mt-3">
                    Start by creating a named session for your event or meeting
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                  <div className="space-y-3">
                    <Label htmlFor="sessionName" className="text-base font-semibold text-gray-700 flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Session Name
                    </Label>
                    <Input
                      id="sessionName"
                      placeholder="e.g., Team Meeting - Dec 2024"
                      value={sessionName}
                      onChange={(e) => setSessionName(e.target.value)}
                      className="input-enhanced h-14 text-lg"
                    />
                  </div>
                  <Button
                    onClick={createSession}
                    disabled={!sessionName.trim()}
                    className="btn-primary w-full h-14 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    <Calendar className="h-5 w-5 mr-3" />
                    Create Session & Generate QR Code
                  </Button>

                  {currentSession && (
                    <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl shadow-soft animate-fade-in">
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-gradient-success rounded-xl flex items-center justify-center flex-shrink-0">
                          <Users className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <p className="text-green-800 font-semibold text-lg">
                            Session "{currentSession.name}" created successfully!
                          </p>
                          <p className="text-green-700 text-base mt-2">
                            Switch to the QR Code tab to display the attendance QR code and start collecting check-ins.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Enhanced QR Code Tab */}
            <TabsContent value="scan" className="animate-scale-in">
              {currentSession ? (
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                  <QRCodeGenerator session={currentSession} />
                  <EnhancedAttendanceScanner onAttendeeAdd={addAttendee} session={currentSession} />
                </div>
              ) : (
                <Card className="card-elevated border-0">
                  <CardContent className="flex flex-col items-center justify-center py-20">
                    <div className="w-24 h-24 bg-gradient-to-br from-gray-200 to-gray-300 rounded-3xl flex items-center justify-center mb-6 shadow-soft">
                      <QrCode className="h-12 w-12 text-gray-500" />
                    </div>
                    <h3 className="text-heading text-gray-700 mb-4">No Active Session</h3>
                    <p className="text-body text-gray-500 text-center max-w-md leading-relaxed">
                      Create a new attendance session first to generate a QR code for check-ins.
                    </p>
                    <Button
                      onClick={() => document.querySelector('[value="create"]')?.click()}
                      className="btn-primary mt-6"
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      Create Session
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Enhanced Attendance Tab */}
            <TabsContent value="attendance" className="animate-scale-in">
              <AttendanceList
                session={currentSession}
                attendanceRecords={attendanceRecords}
              />
            </TabsContent>

            {/* Enhanced Network Settings Tab */}
            <TabsContent value="settings" className="animate-scale-in">
              <NetworkConfig />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Index;
