import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import EnhancedAttendanceScanner from '@/components/EnhancedAttendanceScanner';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  QrCode, 
  AlertCircle, 
  Wifi, 
  WifiOff, 
  Users, 
  Clock,
  Smartphone,
  CheckCircle,
  ArrowLeft
} from 'lucide-react';
import { getSession, addAttendeeToSession, type Session, type Attendee } from '@/utils/localStorage';

const MobileAttendancePage = () => {
  const { sessionId } = useParams();
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [deviceInfo, setDeviceInfo] = useState({
    isMobile: false,
    userAgent: '',
    screenSize: ''
  });

  useEffect(() => {
    // Detect device info
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    setDeviceInfo({
      isMobile,
      userAgent: navigator.userAgent,
      screenSize: `${window.screen.width}x${window.screen.height}`
    });

    // Add mobile viewport meta tag if not present
    if (isMobile && !document.querySelector('meta[name="viewport"]')) {
      const viewport = document.createElement('meta');
      viewport.name = 'viewport';
      viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
      document.head.appendChild(viewport);
    }

    // Online/offline detection
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    if (sessionId) {
      // Load session from localStorage or URL parameters
      let loadedSession = getSession(sessionId);
      
      // If not found locally, try to parse from URL sessionData
      if (!loadedSession) {
        const urlParams = new URLSearchParams(window.location.search);
        const sessionData = urlParams.get('sessionData');
        
        if (sessionData) {
          try {
            const parsedSession = JSON.parse(decodeURIComponent(sessionData));
            loadedSession = parsedSession;
            console.log('Loaded session from QR code data:', loadedSession);
          } catch (error) {
            console.error('Error parsing session data from QR code:', error);
          }
        }
      }

      console.log('Loading session:', sessionId, 'Found:', loadedSession);
      setSession(loadedSession);
    }
    setLoading(false);
  }, [sessionId]);

  const handleAttendeeAdd = (email: string, name: string) => {
    if (session) {
      const newAttendee: Attendee = {
        email,
        name: name || email.split('@')[0],
        timestamp: new Date(),
      };
      
      const updatedSession = addAttendeeToSession(session.id, newAttendee);
      
      if (updatedSession) {
        setSession(updatedSession);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
        <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm w-full max-w-sm">
          <CardContent className="flex flex-col items-center justify-center py-16">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mb-4" />
            <p className="text-gray-600">Loading session...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Mobile-optimized header */}
      <div className="sticky top-0 z-10 bg-white/90 backdrop-blur-sm border-b border-gray-200">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.history.back()}
                className="p-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <QrCode className="h-4 w-4 text-white" />
                </div>
                <h1 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  AttendEase
                </h1>
              </div>
            </div>
            
            {/* Connection status */}
            <div className="flex items-center gap-2">
              {isOnline ? (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <Wifi className="h-3 w-3 mr-1" />
                  Online
                </Badge>
              ) : (
                <Badge variant="destructive">
                  <WifiOff className="h-3 w-3 mr-1" />
                  Offline
                </Badge>
              )}
              
              {deviceInfo.isMobile && (
                <Badge variant="outline">
                  <Smartphone className="h-3 w-3 mr-1" />
                  Mobile
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* Session status card */}
        {session && (
          <Card className="mb-6 shadow-lg border-0 bg-white/90 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-green-700">Active Session</span>
                </div>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Clock className="h-3 w-3" />
                  <span>{new Date().toLocaleTimeString()}</span>
                </div>
              </div>
              
              <h2 className="text-lg font-semibold text-gray-900 mb-2">{session.name}</h2>
              
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>{session.attendees?.length || 0} checked in</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>Created {session.createdAt ? new Date(session.createdAt).toLocaleDateString() : 'Today'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content */}
        <div className="max-w-lg mx-auto">
          {session ? (
            <EnhancedAttendanceScanner onAttendeeAdd={handleAttendeeAdd} session={session} />
          ) : (
            <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="flex flex-col items-center justify-center py-16 px-6">
                <AlertCircle className="h-16 w-16 text-red-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-700 mb-2 text-center">Session Not Found</h3>
                <p className="text-gray-500 text-center mb-6">
                  This attendance session doesn't exist or may have been deleted.
                </p>

                {/* Troubleshooting tips */}
                <div className="w-full space-y-3">
                  <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-900 mb-2">Troubleshooting Tips:</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• Make sure you're on the same network as the organizer</li>
                      <li>• Try refreshing the page</li>
                      <li>• Ask the organizer to regenerate the QR code</li>
                    </ul>
                  </div>

                  {/* Debug Information */}
                  <div className="bg-gray-50 p-3 rounded-lg text-xs text-gray-600">
                    <p><strong>Debug Info:</strong></p>
                    <p>Session ID: {sessionId}</p>
                    <p>Device: {deviceInfo.isMobile ? 'Mobile' : 'Desktop'}</p>
                    <p>Screen: {deviceInfo.screenSize}</p>
                    <p>Online: {isOnline ? 'Yes' : 'No'}</p>
                    <p>Has sessionData: {new URLSearchParams(window.location.search).has('sessionData') ? 'Yes' : 'No'}</p>
                  </div>
                </div>

                <Button 
                  onClick={() => window.location.reload()}
                  className="mt-4 w-full"
                  variant="outline"
                >
                  Refresh Page
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Recent attendees preview (if session exists) */}
        {session && session.attendees && session.attendees.length > 0 && (
          <Card className="mt-6 shadow-lg border-0 bg-white/90 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Recent Check-ins
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {session.attendees.slice(-3).reverse().map((attendee, index) => (
                  <div 
                    key={index}
                    className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div>
                        <span className="font-medium text-green-900">{attendee.name}</span>
                        {attendee.email && (
                          <p className="text-xs text-green-700">{attendee.email}</p>
                        )}
                      </div>
                    </div>
                    <span className="text-green-600 text-xs">
                      {attendee.timestamp ? new Date(attendee.timestamp).toLocaleTimeString() : 'Now'}
                    </span>
                  </div>
                ))}
              </div>
              
              {session.attendees.length > 3 && (
                <p className="text-center text-sm text-gray-500 mt-3">
                  And {session.attendees.length - 3} more attendees...
                </p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Mobile-specific help */}
        <Card className="mt-6 shadow-lg border-0 bg-gradient-to-r from-purple-50 to-blue-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Smartphone className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-purple-800">
                <p className="font-medium mb-1">Mobile Optimized Experience</p>
                <p>This page is designed for mobile devices. Use voice input for hands-free check-in or quick buttons for fastest entry.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Back to Home Link */}
        <div className="text-center mt-8 pb-6">
          <a 
            href="/" 
            className="text-blue-600 hover:text-blue-800 underline text-sm"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  );
};

export default MobileAttendancePage;
