/**
 * Simple sync server for cross-device attendance synchronization
 * Runs alongside Vite dev server to handle real-time sync
 */

import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 8082;

// Store pending check-ins in memory
let pendingCheckIns = [];

// Middleware
app.use(cors());
app.use(express.json());

// Store a new check-in
app.post('/api/checkin', (req, res) => {
  const { sessionId, attendee, timestamp, deviceId } = req.body;
  
  console.log('Received check-in:', { sessionId, attendee: attendee.name, deviceId });
  
  const checkIn = {
    id: Date.now() + '_' + Math.random().toString(36).substr(2, 9),
    sessionId,
    attendee,
    timestamp: new Date(timestamp),
    deviceId,
    createdAt: new Date()
  };
  
  pendingCheckIns.push(checkIn);
  
  // Clean up old check-ins (older than 1 hour)
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  pendingCheckIns = pendingCheckIns.filter(checkIn => checkIn.createdAt > oneHourAgo);
  
  res.json({ success: true, id: checkIn.id });
});

// Get pending check-ins for a session
app.get('/api/checkins/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const { since } = req.query;
  
  let checkIns = pendingCheckIns.filter(checkIn => checkIn.sessionId === sessionId);
  
  // Filter by timestamp if 'since' parameter is provided
  if (since) {
    const sinceDate = new Date(parseInt(since));
    checkIns = checkIns.filter(checkIn => checkIn.createdAt > sinceDate);
  }
  
  console.log(`Returning ${checkIns.length} check-ins for session ${sessionId}`);
  
  res.json({ checkIns });
});

// Get all pending check-ins (for debugging)
app.get('/api/checkins', (req, res) => {
  res.json({ 
    total: pendingCheckIns.length,
    checkIns: pendingCheckIns.map(c => ({
      id: c.id,
      sessionId: c.sessionId,
      attendeeName: c.attendee.name,
      deviceId: c.deviceId,
      createdAt: c.createdAt
    }))
  });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date(),
    pendingCheckIns: pendingCheckIns.length 
  });
});

// Clear all check-ins (for testing)
app.delete('/api/checkins', (req, res) => {
  const count = pendingCheckIns.length;
  pendingCheckIns = [];
  res.json({ success: true, cleared: count });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Attendance Sync Server running on http://localhost:${PORT}`);
  console.log(`📡 Network access: http://***********:${PORT}`);
  console.log(`🔄 Ready to sync attendance data across devices!`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down sync server...');
  process.exit(0);
});
