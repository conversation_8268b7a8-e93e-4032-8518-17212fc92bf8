import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  UserPlus,
  Mail,
  User,
  Mic,
  MicOff,
  Zap,
  Users,
  Smartphone,
  Clock,
  CheckCircle,
  Lightbulb
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { broadcastCheckIn } from '@/utils/realtimeSync';
import {
  getNameSuggestions,
  getEmailSuggestions,
  extractNameFromEmail,
  formatName,
  isValidEmail,
  generateQuickNames,
  processVoiceInput,
  triggerHapticFeedback,
  saveToRecentAttendees,
  type SmartSuggestion
} from '@/utils/smartInput';

// Enhanced Speech recognition hook
const useSpeechRecognition = () => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isSupported, setIsSupported] = useState(false);
  const [error, setError] = useState('');
  const [recognition, setRecognition] = useState(null);
  const [micPermission, setMicPermission] = useState('unknown'); // 'unknown', 'granted', 'denied', 'requesting'

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const isSupported = !!SpeechRecognition;
    setIsSupported(isSupported);

    if (isSupported) {
      console.log('Speech recognition is supported');

      // Automatically request microphone permission for better UX
      // This provides a smoother experience as users don't need to click a button first
      setTimeout(() => {
        requestMicrophonePermissionProactively();
      }, 1000); // Small delay to let the component settle
    } else {
      console.log('Speech recognition is not supported in this browser');
      setError('Speech recognition not supported in this browser');
    }

    // Check current microphone permission status
    checkMicrophonePermission();
  }, []);

  const checkMicrophonePermission = async () => {
    try {
      if (navigator.permissions) {
        const result = await navigator.permissions.query({ name: 'microphone' });
        setMicPermission(result.state);
        console.log('Microphone permission status:', result.state);

        // Listen for permission changes
        result.onchange = () => {
          setMicPermission(result.state);
          console.log('Microphone permission changed to:', result.state);
        };
      }
    } catch (error) {
      console.log('Could not check microphone permission:', error);
    }
  };

  // Check permission status without requesting - respects browser security policies
  const checkMicrophonePermissionStatus = async () => {
    try {
      // Only check permission status, don't request
      if (navigator.permissions) {
        const result = await navigator.permissions.query({ name: 'microphone' });
        setMicPermission(result.state);
        console.log('Microphone permission status checked:', result.state);

        // Listen for permission changes
        result.onchange = () => {
          setMicPermission(result.state);
          console.log('Microphone permission changed to:', result.state);
        };

        return result.state;
      }
    } catch (error) {
      console.log('Could not check microphone permission status:', error);
    }
    return 'unknown';
  };

  // Smart permission request that respects browser policies
  const requestMicrophonePermissionProactively = async () => {
    // Don't request if already granted or denied
    if (micPermission === 'granted' || micPermission === 'denied') {
      return;
    }

    // Don't request if already requesting
    if (micPermission === 'requesting') {
      return;
    }

    // Check current permission status first
    const currentStatus = await checkMicrophonePermissionStatus();

    // If permission is already granted, no need to request
    if (currentStatus === 'granted') {
      setMicPermission('granted');
      return;
    }

    // Modern browsers block automatic permission requests without user interaction
    // So we'll just check status and prepare the UI to encourage user interaction
    console.log('Permission status:', currentStatus, '- Automatic request blocked by browser security');

    // Update UI to show permission is available but needs user interaction
    if (currentStatus === 'prompt' || currentStatus === 'unknown') {
      // Don't automatically request - let user click when ready
      console.log('Permission available but requires user interaction');
    }
  };

  const requestMicrophonePermission = async () => {
    setMicPermission('requesting');
    setError('');

    try {
      console.log('Requesting microphone permission...');

      // Detect browser type for specific handling
      const isBrave = navigator.brave && navigator.brave.isBrave;
      const userAgent = navigator.userAgent;
      const isBraveUA = userAgent.includes('Brave') || userAgent.includes('brave');
      const isLikelyBrave = isBrave || isBraveUA;

      console.log('Browser detection:', {
        isBrave,
        isBraveUA,
        isLikelyBrave,
        userAgent,
        hasNavigator: !!navigator,
        hasMediaDevices: !!navigator.mediaDevices,
        hasGetUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
        mediaDevicesType: typeof navigator.mediaDevices,
        getUserMediaType: navigator.mediaDevices ? typeof navigator.mediaDevices.getUserMedia : 'undefined'
      });

      // Enhanced compatibility check for Brave and other browsers
      let getUserMedia = null;

      // Method 1: Modern getUserMedia
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        getUserMedia = navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);
        console.log('Using modern navigator.mediaDevices.getUserMedia');
      }
      // Method 2: Legacy getUserMedia (fallback for older browsers)
      else if (navigator.getUserMedia) {
        getUserMedia = (constraints) => {
          return new Promise((resolve, reject) => {
            navigator.getUserMedia(constraints, resolve, reject);
          });
        };
        console.log('Using legacy navigator.getUserMedia');
      }
      // Method 3: Webkit prefixed (Safari/older Chrome)
      else if (navigator.webkitGetUserMedia) {
        getUserMedia = (constraints) => {
          return new Promise((resolve, reject) => {
            navigator.webkitGetUserMedia(constraints, resolve, reject);
          });
        };
        console.log('Using webkit prefixed getUserMedia');
      }
      // Method 4: Mozilla prefixed (older Firefox)
      else if (navigator.mozGetUserMedia) {
        getUserMedia = (constraints) => {
          return new Promise((resolve, reject) => {
            navigator.mozGetUserMedia(constraints, resolve, reject);
          });
        };
        console.log('Using mozilla prefixed getUserMedia');
      }

      // If no getUserMedia method is available
      if (!getUserMedia) {
        const errorMsg = isLikelyBrave
          ? '🛡️ Brave Browser: MediaDevices API is blocked. Please enable microphone access in Brave settings: brave://settings/content/microphone'
          : 'getUserMedia not supported in this browser. Please use a modern browser like Chrome, Firefox, or Safari.';
        throw new Error(errorMsg);
      }

      // For Brave browser, try multiple permission request strategies
      if (isLikelyBrave) {
        console.log('Brave browser detected - using enhanced permission request');

        // Strategy 1: Try with minimal constraints first
        try {
          console.log('Brave: Trying minimal audio constraints...');
          const stream = await getUserMedia({ audio: true });
          console.log('Brave: Minimal constraints worked!');

          // Stop tracks if they exist
          if (stream && stream.getTracks) {
            stream.getTracks().forEach(track => track.stop());
          }

          setMicPermission('granted');
          setError('');
          return true;
        } catch (minimalError) {
          console.log('Brave: Minimal constraints failed:', minimalError);

          // Strategy 2: Try with specific constraints
          console.log('Brave: Trying specific audio constraints...');
        }
      }

      // Standard permission request with full constraints
      const stream = await getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      console.log('Microphone permission granted!');
      setMicPermission('granted');
      setError(''); // Clear any previous errors

      // Stop the stream immediately - we just needed permission
      if (stream && stream.getTracks) {
        stream.getTracks().forEach(track => {
          track.stop();
          console.log('Stopped microphone track');
        });
      }

      return true;
    } catch (error) {
      console.error('Microphone permission denied:', error);
      setMicPermission('denied');

      // Enhanced error handling for different browsers
      const isBrave = navigator.brave && navigator.brave.isBrave;
      const userAgent = navigator.userAgent;
      const isLikelyBrave = isBrave || userAgent.includes('Brave') || userAgent.includes('brave');

      let errorMessage = 'Microphone access denied. ';

      if (isLikelyBrave) {
        errorMessage = '🛡️ Brave Browser Issue: ';

        if (error.message && error.message.includes('MediaDevices API is blocked')) {
          errorMessage += 'MediaDevices API is completely blocked. Go to brave://settings/content/microphone and add this site to the "Allow" list, then refresh the page.';
        } else if (error.name === 'NotAllowedError') {
          errorMessage += 'Permission dialog may not have appeared. Try: 1) Click the lock icon 🔒 next to the address bar → Allow microphone, OR 2) Click the shield icon 🛡️ → Allow microphone, then refresh the page.';
        } else if (error.name === 'NotFoundError') {
          errorMessage += 'No microphone detected. Please connect a microphone and try again.';
        } else {
          errorMessage += `${error.name || 'Unknown error'}: Check Brave Shields and site permissions. The MediaDevices API may be blocked.`;
        }
      } else if (error.name === 'NotAllowedError') {
        errorMessage += 'Please click "Allow" when prompted, or enable microphone access in your browser settings.';
      } else if (error.name === 'NotFoundError') {
        errorMessage += 'No microphone found. Please connect a microphone and try again.';
      } else if (error.name === 'NotReadableError') {
        errorMessage += 'Microphone is being used by another application.';
      } else if (error.name === 'AbortError') {
        errorMessage += 'Permission request was cancelled. Please try again.';
      } else if (error.name === 'NotSupportedError') {
        errorMessage += 'Microphone access not supported in this browser.';
      } else {
        errorMessage += error.message || 'Unknown error occurred';
      }

      setError(errorMessage);
      return false;
    }
  };

  const startListening = async () => {
    if (!isSupported) {
      setError('Speech recognition not supported');
      return;
    }

    // Check if we need to request microphone permission first
    if (micPermission !== 'granted') {
      console.log('Requesting microphone permission before starting speech recognition...');
      const permissionGranted = await requestMicrophonePermission();
      if (!permissionGranted) {
        return; // Error message already set by requestMicrophonePermission
      }
    }

    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const newRecognition = new SpeechRecognition();

      // Configure recognition
      newRecognition.continuous = false;
      newRecognition.interimResults = false;
      newRecognition.lang = 'en-US';
      newRecognition.maxAlternatives = 1;

      // Event handlers
      newRecognition.onstart = () => {
        console.log('Speech recognition started');
        setIsListening(true);
        setError('');
      };

      newRecognition.onend = () => {
        console.log('Speech recognition ended');
        setIsListening(false);
      };

      newRecognition.onresult = (event) => {
        console.log('Speech recognition result:', event);
        if (event.results && event.results[0] && event.results[0][0]) {
          const result = event.results[0][0].transcript;
          console.log('Transcript:', result);
          setTranscript(result);
          setError('');
        }
      };

      newRecognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);

        // Handle specific errors
        switch (event.error) {
          case 'not-allowed':
            setError('Microphone access denied. Please allow microphone access and try again.');
            setMicPermission('denied');
            break;
          case 'no-speech':
            setError('No speech detected. Please try speaking again.');
            break;
          case 'network':
            setError('Network error. Please check your internet connection.');
            break;
          case 'audio-capture':
            setError('No microphone found. Please check your microphone connection.');
            break;
          case 'service-not-allowed':
            setError('Speech recognition service not allowed. Please enable it in browser settings.');
            break;
          default:
            setError(`Speech recognition error: ${event.error}`);
        }
      };

      newRecognition.onnomatch = () => {
        console.log('No speech match found');
        setError('Could not understand speech. Please try again.');
      };

      setRecognition(newRecognition);
      newRecognition.start();

    } catch (error) {
      console.error('Error starting speech recognition:', error);
      setError('Failed to start speech recognition');
      setIsListening(false);
    }
  };

  const stopListening = () => {
    if (recognition) {
      recognition.stop();
    }
    setIsListening(false);
  };

  return {
    isListening,
    transcript,
    isSupported,
    error,
    micPermission,
    startListening,
    stopListening,
    setTranscript,
    requestMicrophonePermission,
    requestMicrophonePermissionProactively,
    clearError: () => setError('')
  };
};

const EnhancedAttendanceScanner = ({ onAttendeeAdd, session }) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [inputMethod, setInputMethod] = useState('quick'); // Start with quick method
  const [hasRequestedPermission, setHasRequestedPermission] = useState(false);
  const [recentAttendees, setRecentAttendees] = useState([]);
  const [quickNames, setQuickNames] = useState([]);
  const [nameSuggestions, setNameSuggestions] = useState<SmartSuggestion[]>([]);
  const [emailSuggestions, setEmailSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  const { toast } = useToast();
  const navigate = useNavigate();
  const {
    isListening,
    transcript,
    isSupported,
    error: speechError,
    micPermission,
    startListening,
    stopListening,
    setTranscript,
    requestMicrophonePermission,
    requestMicrophonePermissionProactively,
    clearError
  } = useSpeechRecognition();

  useEffect(() => {
    // Load recent attendees from localStorage
    const recent = JSON.parse(localStorage.getItem('recentAttendees') || '[]');
    setRecentAttendees(recent.slice(0, 6));

    // Generate context-aware quick names
    const contextNames = generateQuickNames(session.name);
    setQuickNames(contextNames);
  }, [session.name]);

  useEffect(() => {
    // Enhanced mobile experience with automatic permission handling
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const urlParams = new URLSearchParams(window.location.search);
    const isFromQRCode = urlParams.has('sessionData');

    // Auto-switch to voice method and show permission prompt for mobile QR scans
    if (isMobile && isFromQRCode && isSupported && !hasRequestedPermission) {
      console.log('Mobile QR scan detected - optimizing for voice input...');
      setHasRequestedPermission(true);

      // Switch to voice method immediately for mobile users
      setInputMethod('voice');

      // Show encouraging toast for mobile users
      setTimeout(() => {
        if (micPermission === 'granted') {
          toast({
            title: "🎤 Voice Input Ready!",
            description: "You can now speak your name to check in quickly!",
          });
        } else if (micPermission !== 'requesting') {
          toast({
            title: "🎤 Voice Check-in Available",
            description: "Tap 'Allow Microphone' for hands-free check-in!",
          });
        }
      }, 1500);
    }

    // For desktop users, show a subtle notification about voice availability
    if (!isMobile && isSupported && micPermission === 'granted' && inputMethod !== 'voice') {
      setTimeout(() => {
        toast({
          title: "💡 Voice Input Available",
          description: "Switch to Voice tab for hands-free check-in!",
        });
      }, 3000);
    }
  }, [isSupported, hasRequestedPermission, micPermission, inputMethod, toast]);

  useEffect(() => {
    // Auto-fill name when voice transcript is available
    if (transcript && inputMethod === 'voice') {
      const processedName = processVoiceInput(transcript);
      setName(processedName);
      setTranscript('');
      triggerHapticFeedback('success');

      // Show success toast
      toast({
        title: "Voice Recognition Success! 🎤",
        description: `Heard: "${processedName}"`,
      });
    }
  }, [transcript, inputMethod, setTranscript, toast]);

  useEffect(() => {
    // Show error toast for speech recognition errors
    if (speechError && inputMethod === 'voice') {
      toast({
        title: "Voice Recognition Error",
        description: speechError,
        variant: "destructive"
      });
    }
  }, [speechError, inputMethod, toast]);

  useEffect(() => {
    // Generate name suggestions as user types
    if (name.length >= 2) {
      const suggestions = getNameSuggestions(name, 5);
      setNameSuggestions(suggestions);
      setShowSuggestions(suggestions.length > 0);
    } else {
      setNameSuggestions([]);
      setShowSuggestions(false);
    }
  }, [name]);

  useEffect(() => {
    // Generate email suggestions as user types
    if (email.includes('@') && email.split('@')[1].length >= 1) {
      const suggestions = getEmailSuggestions(email, 3);
      setEmailSuggestions(suggestions);
    } else {
      setEmailSuggestions([]);
    }

    // Auto-extract name from email
    if (email && isValidEmail(email) && !name) {
      const extractedName = extractNameFromEmail(email);
      if (extractedName) {
        setName(extractedName);
      }
    }
  }, [email, name]);

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const saveRecentAttendee = (attendee) => {
    saveToRecentAttendees(attendee);
    const recent = JSON.parse(localStorage.getItem('recentAttendees') || '[]');
    setRecentAttendees(recent.slice(0, 6));
  };

  const handleNameSuggestionClick = (suggestion: SmartSuggestion) => {
    setName(suggestion.name);
    if (suggestion.email) {
      setEmail(suggestion.email);
    }
    setShowSuggestions(false);
    triggerHapticFeedback('success');
  };

  const handleEmailSuggestionClick = (suggestedEmail: string) => {
    setEmail(suggestedEmail);
    setEmailSuggestions([]);
    triggerHapticFeedback('success');
  };

  const handleQuickCheckIn = async (quickName) => {
    // Check if already checked in
    const alreadyCheckedIn = session.attendees.some(attendee => attendee.name === quickName);

    if (alreadyCheckedIn) {
      triggerHapticFeedback('error');
      toast({
        title: "Already Checked In",
        description: `${quickName} has already been recorded for this session`,
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    triggerHapticFeedback('success');

    try {
      await new Promise(resolve => setTimeout(resolve, 800));

      const newAttendee = {
        email: '',
        name: quickName,
        timestamp: new Date(),
      };

      await processCheckIn(newAttendee);
      saveRecentAttendee(newAttendee);
      triggerHapticFeedback('success');
    } catch (error) {
      triggerHapticFeedback('error');
      toast({
        title: "Check-in Failed",
        description: "Please try again",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const processCheckIn = async (attendee) => {
    const urlParams = new URLSearchParams(window.location.search);
    const isFromQRCode = urlParams.has('sessionData');

    if (isFromQRCode) {
      await broadcastCheckIn(session.id, attendee);
      const successUrl = `/success/${session.id}?name=${encodeURIComponent(attendee.name)}&session=${encodeURIComponent(session.name)}&time=${encodeURIComponent(new Date().toLocaleString())}`;
      navigate(successUrl);
    } else {
      onAttendeeAdd(attendee.email, attendee.name);
      toast({
        title: "Check-in Successful! 🎉",
        description: `Welcome ${attendee.name}! Your attendance has been recorded.`,
      });
      setEmail('');
      setName('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email.trim() && !name.trim()) {
      toast({
        title: "Information Required",
        description: "Please enter either your email address or your name",
        variant: "destructive"
      });
      return;
    }

    if (email.trim() && !validateEmail(email)) {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address",
        variant: "destructive"
      });
      return;
    }

    const identifier = email.trim() || name.trim();
    const alreadyCheckedIn = session.attendees.some(attendee =>
      (email.trim() && attendee.email === email) ||
      (!email.trim() && attendee.name === name)
    );

    if (alreadyCheckedIn) {
      toast({
        title: "Already Checked In",
        description: `${identifier} has already been recorded for this session`,
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const finalEmail = email.trim() || '';
      const finalName = name.trim() || (email.trim() ? email.split('@')[0] : '');

      const newAttendee = {
        email: finalEmail,
        name: finalName,
        timestamp: new Date(),
      };

      await processCheckIn(newAttendee);
      saveRecentAttendee(newAttendee);
    } catch (error) {
      toast({
        title: "Check-in Failed",
        description: "Please try again",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2 text-xl text-gray-800">
          <UserPlus className="h-6 w-6 text-green-600" />
          Quick Check-in
        </CardTitle>
        <p className="text-sm text-gray-600">Choose your preferred check-in method</p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Microphone Permission Status */}
        {isSupported && (
          <div className={`p-3 rounded-lg border ${
            micPermission === 'granted'
              ? 'bg-green-50 border-green-200'
              : micPermission === 'denied'
              ? 'bg-red-50 border-red-200'
              : micPermission === 'requesting'
              ? 'bg-yellow-50 border-yellow-200'
              : 'bg-blue-50 border-blue-200'
          }`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Mic className={`h-4 w-4 ${
                  micPermission === 'granted'
                    ? 'text-green-600'
                    : micPermission === 'denied'
                    ? 'text-red-600'
                    : micPermission === 'requesting'
                    ? 'text-yellow-600'
                    : 'text-blue-600'
                }`} />
                <span className={`text-sm font-medium ${
                  micPermission === 'granted'
                    ? 'text-green-800'
                    : micPermission === 'denied'
                    ? 'text-red-800'
                    : micPermission === 'requesting'
                    ? 'text-yellow-800'
                    : 'text-blue-800'
                }`}>
                  {micPermission === 'granted'
                    ? '🎤 Voice input ready'
                    : micPermission === 'denied'
                    ? '🚫 Microphone access denied'
                    : micPermission === 'requesting'
                    ? '⏳ Requesting microphone access...'
                    : '🎤 Voice input available - click to enable'
                  }
                </span>
              </div>
              {micPermission === 'granted' && inputMethod !== 'voice' && (
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={() => setInputMethod('voice')}
                  className="text-green-700 border-green-300 hover:bg-green-100"
                >
                  Use Voice
                </Button>
              )}
              {(micPermission === 'denied' || micPermission === 'unknown' || micPermission === 'prompt') && (
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={requestMicrophonePermission}
                  className={`${
                    micPermission === 'denied'
                      ? 'text-red-700 border-red-300 hover:bg-red-100'
                      : 'text-blue-700 border-blue-300 hover:bg-blue-100 animate-pulse'
                  }`}
                >
                  {micPermission === 'denied' ? 'Try Again' : 'Enable Voice'}
                </Button>
              )}
            </div>
            {(micPermission === 'unknown' || micPermission === 'prompt') && (
              <p className="text-blue-700 text-xs mt-2">
                💡 Click "Enable Voice" to allow microphone access for hands-free check-in
              </p>
            )}
          </div>
        )}

        {/* Session Info */}
        <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
          <p className="text-blue-800 font-medium">Checking in for:</p>
          <p className="text-blue-900 text-lg font-semibold">{session.name}</p>
          <div className="flex items-center justify-center gap-2 mt-2 text-sm text-blue-700">
            <Clock className="h-4 w-4" />
            <span>{new Date().toLocaleTimeString()}</span>
          </div>
        </div>

        {/* Voice Available Notification */}
        {micPermission === 'granted' && inputMethod !== 'voice' && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Mic className="h-4 w-4 text-green-600" />
                <span className="text-green-800 text-sm font-medium">
                  🎤 Voice input is ready!
                </span>
              </div>
              <Button
                type="button"
                size="sm"
                variant="outline"
                onClick={() => setInputMethod('voice')}
                className="text-green-700 border-green-300 hover:bg-green-100"
              >
                Try Voice
              </Button>
            </div>
            <p className="text-green-700 text-xs mt-1">
              Switch to Voice tab to speak your name hands-free.
            </p>
          </div>
        )}

        {/* Voice Permission Prompt - Enhanced for all users */}
        {(() => {
          const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
          const urlParams = new URLSearchParams(window.location.search);
          const isFromQRCode = urlParams.has('sessionData');

          // Show for mobile QR scans OR when permission is available but not granted
          const shouldShow = (isMobile && isFromQRCode && isSupported && micPermission !== 'granted') ||
                           (!isMobile && isSupported && (micPermission === 'unknown' || micPermission === 'prompt') && inputMethod !== 'voice');

          return shouldShow;
        })() && (
          <div className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 border-2 border-purple-300 rounded-xl shadow-lg">
            <div className="text-center space-y-3">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto animate-pulse">
                <Mic className="h-8 w-8 text-white" />
              </div>
              <div>
                <p className="text-purple-900 text-xl font-bold">
                  🎤 Try Voice Check-in!
                </p>
                <p className="text-purple-700 text-sm mt-1">
                  Speak your name instead of typing - it's faster and easier!
                </p>
                <p className="text-purple-600 text-xs mt-2">
                  {micPermission === 'denied'
                    ? 'Permission was denied - click to try again'
                    : 'Click below and allow microphone access when prompted'
                  }
                </p>
              </div>
              <Button
                type="button"
                size="lg"
                onClick={() => {
                  setInputMethod('voice');
                  // Also trigger permission request immediately
                  if (micPermission !== 'granted') {
                    setTimeout(() => requestMicrophonePermission(), 100);
                  }
                }}
                className="w-full h-12 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                <Mic className="h-5 w-5 mr-2" />
                {micPermission === 'denied' ? 'Try Voice Again' : 'Enable Voice Input'}
              </Button>
              <p className="text-purple-600 text-xs">
                {micPermission === 'denied'
                  ? 'Check browser settings if permission keeps failing'
                  : 'Your browser will show a permission popup - click "Allow"'
                }
              </p>
            </div>
          </div>
        )}

        {/* Input Method Selector */}
        <div className="flex gap-2 p-1 bg-gray-100 rounded-lg">
          <Button
            type="button"
            variant={inputMethod === 'quick' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setInputMethod('quick')}
            className="flex-1"
          >
            <Zap className="h-4 w-4 mr-1" />
            Quick
          </Button>
          <Button
            type="button"
            variant={inputMethod === 'manual' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setInputMethod('manual')}
            className="flex-1"
          >
            <User className="h-4 w-4 mr-1" />
            Manual
          </Button>
          <Button
            type="button"
            variant={inputMethod === 'voice' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setInputMethod('voice')}
            className={`flex-1 relative ${micPermission === 'granted' ? 'ring-2 ring-green-300' : ''}`}
            disabled={!isSupported}
          >
            <Mic className="h-4 w-4 mr-1" />
            Voice {!isSupported && '(N/A)'}
            {micPermission === 'granted' && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            )}
          </Button>
        </div>

        {/* Quick Check-in */}
        {inputMethod === 'quick' && (
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-gray-700 mb-3 block">
                <Zap className="h-4 w-4 inline mr-1" />
                One-Click Check-in
              </Label>
              <div className="grid grid-cols-2 gap-2">
                {quickNames.map((quickName, index) => (
                  <Button
                    key={index}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickCheckIn(quickName)}
                    disabled={isSubmitting}
                    className="text-left justify-start h-auto py-2 px-3 hover:bg-blue-50 hover:border-blue-300"
                  >
                    <User className="h-3 w-3 mr-2 text-blue-600" />
                    <span className="text-sm">{quickName}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Recent Attendees */}
            {recentAttendees.length > 0 && (
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-3 block">
                  <Users className="h-4 w-4 inline mr-1" />
                  Recent Check-ins
                </Label>
                <div className="grid grid-cols-1 gap-2">
                  {recentAttendees.map((attendee, index) => (
                    <Button
                      key={index}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickCheckIn(attendee.name)}
                      disabled={isSubmitting}
                      className="text-left justify-start h-auto py-2 px-3 hover:bg-green-50 hover:border-green-300"
                    >
                      <CheckCircle className="h-3 w-3 mr-2 text-green-600" />
                      <span className="text-sm">{attendee.name}</span>
                      {attendee.email && (
                        <Badge variant="secondary" className="ml-auto text-xs">
                          {attendee.email.split('@')[0]}
                        </Badge>
                      )}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Voice Input */}
        {inputMethod === 'voice' && (
          <div className="space-y-4">
            {/* Voice Recognition Status */}
            <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg border border-purple-200">
              <div className="mb-4">
                {isListening ? (
                  <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto animate-pulse">
                    <Mic className="h-8 w-8 text-white" />
                  </div>
                ) : (
                  <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto">
                    <MicOff className="h-8 w-8 text-white" />
                  </div>
                )}
              </div>

              <p className="text-purple-800 font-medium mb-2">
                {isListening ? 'Listening... Speak your name clearly' : 'Tap to speak your name'}
              </p>

              {/* Speech Recognition Support Status */}
              {!isSupported && (
                <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-yellow-800 text-sm">
                    ⚠️ Speech recognition is not supported in this browser. Try Chrome, Edge, or Safari.
                  </p>
                </div>
              )}

              {/* Microphone Permission Status */}
              {isSupported && micPermission !== 'granted' && (
                <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-300 rounded-xl shadow-lg">
                  <div className="text-center space-y-3">
                    <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto">
                      <Mic className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <p className="text-blue-900 text-lg font-bold">
                        🎤 Enable Voice Input
                      </p>
                      <p className="text-blue-700 text-sm mt-1">
                        {micPermission === 'denied'
                          ? 'Permission was denied. Please enable microphone access in your browser settings.'
                          : micPermission === 'requesting'
                          ? 'Requesting permission...'
                          : 'Tap below to allow microphone access for hands-free check-in!'
                        }
                      </p>
                    </div>
                    {micPermission !== 'requesting' && micPermission !== 'denied' && (
                      <Button
                        type="button"
                        size="lg"
                        onClick={requestMicrophonePermission}
                        className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold text-lg shadow-lg transform hover:scale-105 transition-all duration-200"
                      >
                        <Mic className="h-5 w-5 mr-2" />
                        Allow Microphone Access
                      </Button>
                    )}
                    {micPermission === 'requesting' && (
                      <Button
                        type="button"
                        size="lg"
                        disabled
                        className="w-full h-12 bg-gray-400 text-white font-bold text-lg"
                      >
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Requesting Permission...
                      </Button>
                    )}
                  </div>
                </div>
              )}

              {/* Permission Granted Status */}
              {micPermission === 'granted' && (
                <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 text-sm">
                    ✅ Microphone access granted! You can now use voice input.
                  </p>
                </div>
              )}

              {/* Error Display */}
              {speechError && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800 text-sm font-medium">Error:</p>
                  <p className="text-red-700 text-sm">{speechError}</p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={clearError}
                    className="mt-2 text-red-600 border-red-300 hover:bg-red-50"
                  >
                    Dismiss
                  </Button>
                </div>
              )}

              {/* Success Display */}
              {transcript && !speechError && (
                <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 text-sm font-medium">Heard:</p>
                  <p className="text-green-700 text-sm">"{transcript}"</p>
                </div>
              )}

              <Button
                type="button"
                onClick={isListening ? stopListening : startListening}
                disabled={isSubmitting || !isSupported || (micPermission !== 'granted' && micPermission !== 'requesting')}
                className={`${isListening ? 'bg-red-500 hover:bg-red-600' : 'bg-purple-500 hover:bg-purple-600'} text-white disabled:bg-gray-400`}
              >
                {micPermission === 'requesting' ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Requesting Permission...
                  </>
                ) : isListening ? (
                  <>
                    <MicOff className="h-4 w-4 mr-2" />
                    Stop Recording
                  </>
                ) : (
                  <>
                    <Mic className="h-4 w-4 mr-2" />
                    {!isSupported
                      ? 'Not Supported'
                      : micPermission !== 'granted'
                      ? 'Permission Required'
                      : 'Start Recording'
                    }
                  </>
                )}
              </Button>

              {/* Browser-Specific Instructions */}
              <div className="mt-4 text-xs space-y-2">
                {(() => {
                  const isBrave = navigator.brave && navigator.brave.isBrave;
                  const userAgent = navigator.userAgent;
                  const isLikelyBrave = isBrave || userAgent.includes('Brave') || userAgent.includes('brave');

                  if (isLikelyBrave && micPermission !== 'granted') {
                    return (
                      <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                        <p className="text-orange-800 font-medium">🛡️ Brave Browser Detected</p>
                        <div className="text-orange-700 text-xs mt-1 space-y-2">
                          <p><strong>MediaDevices API blocked? Try these steps:</strong></p>
                          <div className="space-y-1 ml-2">
                            <p>1. Click the lock icon (🔒) next to the address bar</p>
                            <p>2. Find "Microphone" and change to "Allow"</p>
                            <p>3. Refresh the page and try again</p>
                          </div>
                          <p><strong>If that doesn't work:</strong></p>
                          <div className="space-y-1 ml-2">
                            <p>1. Look for the shield icon (🛡️) in the address bar</p>
                            <p>2. Click it and select "Allow microphone"</p>
                            <p>3. Or type: brave://settings/content/microphone</p>
                            <p>4. Add this site to the "Allow" list</p>
                          </div>
                          <div className="flex gap-2 mt-2">
                            <Button
                              type="button"
                              size="sm"
                              onClick={requestMicrophonePermission}
                              className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
                            >
                              🔄 Try Again
                            </Button>
                            <Button
                              type="button"
                              size="sm"
                              onClick={() => window.open('brave://settings/content/microphone', '_blank')}
                              className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
                            >
                              ⚙️ Settings
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  }

                  return (
                    <div className="text-purple-600">
                      <p>💡 <strong>Tips:</strong></p>
                      <p>• Speak clearly and slowly</p>
                      <p>• Say your full name (e.g., "John Smith")</p>
                      <p>• Make sure your microphone is enabled</p>
                    </div>
                  );
                })()}
              </div>
            </div>
            
            {/* Voice input form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="voice-name" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <User className="h-4 w-4" />
                  Your Name (from voice)
                </Label>
                <Input
                  id="voice-name"
                  type="text"
                  placeholder="Your name will appear here..."
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="h-12 text-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="voice-email" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                  <Mail className="h-4 w-4" />
                  Email (Optional)
                </Label>
                <Input
                  id="voice-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="h-12 text-lg border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
              
              <Button
                type="submit"
                disabled={isSubmitting || (!email.trim() && !name.trim())}
                className="w-full h-12 text-lg bg-purple-600 hover:bg-purple-700 text-white font-medium transition-all duration-200"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Checking In...
                  </div>
                ) : (
                  <>
                    <UserPlus className="h-5 w-5 mr-2" />
                    Check In to Session
                  </>
                )}
              </Button>
            </form>
          </div>
        )}

        {/* Manual Input */}
        {inputMethod === 'manual' && (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2 relative">
              <Label htmlFor="email" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <Mail className="h-4 w-4" />
                Email Address (Optional)
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="h-12 text-lg border-gray-200 focus:border-blue-500 focus:ring-blue-500"
              />

              {/* Email Suggestions */}
              {emailSuggestions.length > 0 && (
                <div className="absolute z-10 w-full bg-white border border-gray-200 rounded-lg shadow-lg mt-1">
                  {emailSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => handleEmailSuggestionClick(suggestion)}
                      className="w-full text-left px-3 py-2 hover:bg-blue-50 first:rounded-t-lg last:rounded-b-lg text-sm"
                    >
                      <Mail className="h-3 w-3 inline mr-2 text-blue-500" />
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2 relative">
              <Label htmlFor="name" className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <User className="h-4 w-4" />
                Full Name
                {showSuggestions && (
                  <Badge variant="secondary" className="ml-auto text-xs">
                    <Lightbulb className="h-3 w-3 mr-1" />
                    {nameSuggestions.length} suggestions
                  </Badge>
                )}
              </Label>
              <Input
                id="name"
                type="text"
                placeholder="Your full name"
                value={name}
                onChange={(e) => setName(formatName(e.target.value))}
                className="h-12 text-lg border-gray-200 focus:border-blue-500 focus:ring-blue-500"
              />

              {/* Name Suggestions */}
              {showSuggestions && nameSuggestions.length > 0 && (
                <div className="absolute z-10 w-full bg-white border border-gray-200 rounded-lg shadow-lg mt-1">
                  {nameSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => handleNameSuggestionClick(suggestion)}
                      className="w-full text-left px-3 py-2 hover:bg-blue-50 first:rounded-t-lg last:rounded-b-lg"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <User className="h-3 w-3 text-blue-500" />
                          <span className="text-sm font-medium">{suggestion.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Badge
                            variant={suggestion.source === 'recent' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {suggestion.source}
                          </Badge>
                          {suggestion.email && (
                            <span className="text-xs text-gray-500">{suggestion.email.split('@')[0]}</span>
                          )}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            <Button
              type="submit"
              disabled={isSubmitting || (!email.trim() && !name.trim())}
              className="w-full h-12 text-lg bg-blue-600 hover:bg-blue-700 text-white font-medium transition-all duration-200 transform hover:scale-[1.02] disabled:transform-none"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Checking In...
                </div>
              ) : (
                <>
                  <UserPlus className="h-5 w-5 mr-2" />
                  Check In to Session
                </>
              )}
            </Button>
          </form>
        )}

        {/* Mobile Optimized Tips */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 p-3 rounded-lg border border-green-200">
          <div className="flex items-start gap-2">
            <Smartphone className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-green-800 space-y-1">
              <p><strong>Quick Tips:</strong></p>
              <p>• Use "Quick" for fastest check-in with common names</p>
              <p>• Use "Voice" to speak your name hands-free</p>
              <p>• Use "Manual" for custom names and emails</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedAttendanceScanner;
