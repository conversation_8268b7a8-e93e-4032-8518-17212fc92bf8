/**
 * Real-time synchronization for cross-device attendance
 * Uses HTTP-based sync server for true cross-device communication
 */

import { type Attendee, addAttendeeToSession } from './localStorage';
import { getNetworkUrlSync } from './networkUtils';

// Sync server configuration
const SYNC_SERVER_PORT = 8082;
let syncServerUrl = '';
let pollingInterval: NodeJS.Timeout | null = null;
let lastSyncTime = Date.now();

export interface SyncMessage {
  type: 'NEW_CHECKIN' | 'PING' | 'PONG';
  sessionId: string;
  attendee?: Attendee;
  timestamp: number;
  deviceId: string;
}

/**
 * Initialize real-time sync
 */
export const initializeRealtimeSync = (onSyncReceived?: (message: SyncMessage) => void): void => {
  // Initialize sync server URL
  const baseUrl = getNetworkUrlSync().replace(':8080', `:${SYNC_SERVER_PORT}`);
  syncServerUrl = baseUrl;

  console.log('Initializing sync with server:', syncServerUrl);

  // Test server connection
  testServerConnection();

  // Start polling for new check-ins
  startPolling(onSyncReceived);

  console.log('Real-time sync initialized with HTTP server');
};

/**
 * Send check-in to sync server
 */
export const broadcastCheckIn = async (sessionId: string, attendee: Attendee): Promise<void> => {
  const checkInData = {
    sessionId,
    attendee,
    timestamp: Date.now(),
    deviceId: getDeviceId()
  };

  try {
    const serverUrl = getSyncServerUrl();
    console.log('Sending check-in to server:', serverUrl, checkInData);

    const response = await fetch(`${serverUrl}/api/checkin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(checkInData)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Check-in sent successfully:', result);
    } else {
      console.error('Failed to send check-in:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('Error sending check-in to server:', error);
  }
};

/**
 * Test server connection
 */
const testServerConnection = async (): Promise<void> => {
  try {
    const serverUrl = getSyncServerUrl();
    const response = await fetch(`${serverUrl}/api/health`);
    if (response.ok) {
      const data = await response.json();
      console.log('Sync server connected:', data);
    } else {
      console.warn('Sync server not responding:', response.status);
    }
  } catch (error) {
    console.warn('Sync server not available:', error);
  }
};

/**
 * Start polling for new check-ins
 */
const startPolling = (onSyncReceived?: (message: SyncMessage) => void): void => {
  // Clear any existing polling
  if (pollingInterval) {
    clearInterval(pollingInterval);
  }

  // Poll every 2 seconds for new check-ins
  pollingInterval = setInterval(async () => {
    await pollForCheckIns(onSyncReceived);
  }, 2000);

  console.log('Started polling for check-ins');
};

/**
 * Poll server for new check-ins
 */
const pollForCheckIns = async (onSyncReceived?: (message: SyncMessage) => void): Promise<void> => {
  try {
    const serverUrl = getSyncServerUrl();
    // Get all sessions to check for new check-ins
    const sessions = JSON.parse(localStorage.getItem('qr_attendance_sessions') || '[]');

    console.log(`Polling for check-ins. Found ${sessions.length} local sessions.`);

    for (const session of sessions) {
      const pollUrl = `${serverUrl}/api/checkins/${session.id}?since=${lastSyncTime}`;
      console.log(`Polling session ${session.id} at:`, pollUrl);

      const response = await fetch(pollUrl);

      if (response.ok) {
        const data = await response.json();
        console.log(`Found ${data.checkIns.length} check-ins for session ${session.id}`);

        for (const checkIn of data.checkIns) {
          // Don't process our own check-ins
          if (checkIn.deviceId === getDeviceId()) {
            console.log('Skipping own check-in:', checkIn.deviceId);
            continue;
          }

          console.log('Processing new check-in from server:', checkIn);

          // Add to local session
          const result = addAttendeeToSession(checkIn.sessionId, checkIn.attendee);

          if (result && onSyncReceived) {
            console.log('Successfully added attendee, triggering sync callback');
            const message: SyncMessage = {
              type: 'NEW_CHECKIN',
              sessionId: checkIn.sessionId,
              attendee: checkIn.attendee,
              timestamp: checkIn.timestamp,
              deviceId: checkIn.deviceId
            };
            onSyncReceived(message);
          } else {
            console.log('Failed to add attendee or no sync callback');
          }
        }
      } else {
        console.error(`Failed to poll session ${session.id}:`, response.status, response.statusText);
      }
    }

    lastSyncTime = Date.now();
  } catch (error) {
    console.error('Error polling for check-ins:', error);
  }
};

/**
 * Get or create a unique device ID
 */
export const getDeviceId = (): string => {
  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = 'device_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
};

/**
 * Clean up sync resources
 */
export const cleanupRealtimeSync = (): void => {
  if (pollingInterval) {
    clearInterval(pollingInterval);
    pollingInterval = null;
  }

  console.log('Cleaned up real-time sync');
};

/**
 * Get sync server URL
 */
const getSyncServerUrl = (): string => {
  if (!syncServerUrl) {
    const baseUrl = getNetworkUrlSync().replace(':8080', `:${SYNC_SERVER_PORT}`);
    syncServerUrl = baseUrl;
  }
  return syncServerUrl;
};

/**
 * Test if real-time sync is working
 */
export const testSync = async (): Promise<void> => {
  try {
    const serverUrl = getSyncServerUrl();
    console.log('Testing sync server at:', serverUrl);

    const response = await fetch(`${serverUrl}/api/health`);
    if (response.ok) {
      const data = await response.json();
      console.log('Sync server is working!', data);
    } else {
      console.error('Sync server test failed:', response.status);
      throw new Error(`Server responded with status ${response.status}`);
    }
  } catch (error) {
    console.error('Sync server test error:', error);
    throw error;
  }
};

/**
 * Manually trigger a sync check (for debugging)
 */
export const manualSync = async (onSyncReceived?: (message: SyncMessage) => void): Promise<void> => {
  console.log('Manual sync triggered');
  await pollForCheckIns(onSyncReceived);
};

/**
 * Clear all pending check-ins on server (for testing)
 */
export const clearPendingCheckIns = async (): Promise<void> => {
  try {
    const serverUrl = getSyncServerUrl();
    const response = await fetch(`${serverUrl}/api/checkins`, {
      method: 'DELETE'
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Cleared pending check-ins:', data);
    }
  } catch (error) {
    console.error('Error clearing pending check-ins:', error);
  }
};
