import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  TrendingUp, 
  Clock, 
  Users, 
  Calendar,
  Download,
  PieChart,
  Activity,
  Target,
  Award
} from 'lucide-react';

interface Attendee {
  name: string;
  email?: string;
  timestamp: Date;
}

interface Session {
  id: string;
  name: string;
  createdAt: Date;
  attendees: Attendee[];
}

interface AnalyticsReportsProps {
  session: Session | null;
  attendanceRecords: Attendee[];
}

const AnalyticsReports = ({ session, attendanceRecords }: AnalyticsReportsProps) => {
  // Calculate analytics data
  const getAnalytics = () => {
    if (!session || !session.attendees.length) {
      return {
        totalAttendees: 0,
        averageCheckInTime: 'N/A',
        peakHour: 'N/A',
        attendanceRate: 0,
        hourlyDistribution: [],
        recentTrend: 'stable'
      };
    }

    const attendees = session.attendees;
    const totalAttendees = attendees.length;
    
    // Calculate average check-in time
    const times = attendees.map(a => a.timestamp.getTime());
    const avgTime = new Date(times.reduce((a, b) => a + b, 0) / times.length);
    
    // Calculate hourly distribution
    const hourlyCount: { [key: number]: number } = {};
    attendees.forEach(attendee => {
      const hour = attendee.timestamp.getHours();
      hourlyCount[hour] = (hourlyCount[hour] || 0) + 1;
    });
    
    // Find peak hour
    const peakHour = Object.entries(hourlyCount)
      .reduce((a, b) => hourlyCount[parseInt(a[0])] > hourlyCount[parseInt(b[0])] ? a : b)[0];
    
    // Calculate trend (simplified)
    const recentAttendees = attendees.filter(a => 
      Date.now() - a.timestamp.getTime() < 30 * 60 * 1000 // Last 30 minutes
    ).length;
    
    const trend = recentAttendees > totalAttendees * 0.3 ? 'increasing' : 
                  recentAttendees < totalAttendees * 0.1 ? 'decreasing' : 'stable';

    return {
      totalAttendees,
      averageCheckInTime: avgTime.toLocaleTimeString(),
      peakHour: `${peakHour}:00`,
      attendanceRate: 85, // Placeholder - could be calculated based on expected vs actual
      hourlyDistribution: Object.entries(hourlyCount).map(([hour, count]) => ({
        hour: parseInt(hour),
        count
      })),
      recentTrend: trend
    };
  };

  const analytics = getAnalytics();

  const exportAnalytics = () => {
    if (!session) return;
    
    const analyticsData = {
      sessionName: session.name,
      sessionDate: session.createdAt.toLocaleDateString(),
      totalAttendees: analytics.totalAttendees,
      averageCheckInTime: analytics.averageCheckInTime,
      peakHour: analytics.peakHour,
      attendanceRate: analytics.attendanceRate,
      hourlyDistribution: analytics.hourlyDistribution,
      attendeeList: session.attendees.map(a => ({
        name: a.name,
        email: a.email || 'N/A',
        checkInTime: a.timestamp.toLocaleString()
      }))
    };

    const jsonContent = JSON.stringify(analyticsData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.href = url;
    link.download = `${session.name}_analytics.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (!session) {
    return (
      <Card className="card-elevated border-0">
        <CardContent className="flex flex-col items-center justify-center py-20">
          <div className="w-24 h-24 bg-gradient-to-br from-gray-200 to-gray-300 rounded-3xl flex items-center justify-center mb-6 shadow-soft">
            <BarChart3 className="h-12 w-12 text-gray-500" />
          </div>
          <h3 className="text-heading text-gray-700 mb-4">No Session Data</h3>
          <p className="text-body text-gray-500 text-center max-w-md leading-relaxed">
            Create an attendance session to view analytics and reports.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8 animate-fade-in">
      {/* Analytics Overview */}
      <Card className="card-elevated border-0">
        <CardHeader className="pb-6">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-3 text-heading text-gray-800">
              <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              Session Analytics
            </CardTitle>
            <Button 
              onClick={exportAnalytics}
              variant="outline"
              className="border-blue-300 text-blue-700 hover:bg-blue-50 rounded-xl font-medium"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Attendees */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-2xl text-center border border-blue-200 shadow-soft">
              <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center mx-auto mb-3">
                <Users className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold text-blue-900 text-2xl mb-1">{analytics.totalAttendees}</h3>
              <p className="text-caption text-blue-700">Total Attendees</p>
            </div>

            {/* Average Check-in Time */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-2xl text-center border border-green-200 shadow-soft">
              <div className="w-12 h-12 bg-gradient-success rounded-xl flex items-center justify-center mx-auto mb-3">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold text-green-900 text-lg mb-1">{analytics.averageCheckInTime}</h3>
              <p className="text-caption text-green-700">Avg Check-in Time</p>
            </div>

            {/* Peak Hour */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-2xl text-center border border-purple-200 shadow-soft">
              <div className="w-12 h-12 bg-gradient-secondary rounded-xl flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold text-purple-900 text-lg mb-1">{analytics.peakHour}</h3>
              <p className="text-caption text-purple-700">Peak Hour</p>
            </div>

            {/* Attendance Rate */}
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-2xl text-center border border-orange-200 shadow-soft">
              <div className="w-12 h-12 bg-gradient-warm rounded-xl flex items-center justify-center mx-auto mb-3">
                <Target className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold text-orange-900 text-2xl mb-1">{analytics.attendanceRate}%</h3>
              <p className="text-caption text-orange-700">Attendance Rate</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Trends and Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Check-in Trends */}
        <Card className="card-elevated border-0">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center gap-3 text-heading text-gray-800">
              <div className="w-10 h-10 bg-gradient-success rounded-xl flex items-center justify-center">
                <Activity className="h-5 w-5 text-white" />
              </div>
              Check-in Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200">
                <div className="flex items-center gap-3">
                  <TrendingUp className={`h-5 w-5 ${
                    analytics.recentTrend === 'increasing' ? 'text-green-600' :
                    analytics.recentTrend === 'decreasing' ? 'text-red-600' : 'text-blue-600'
                  }`} />
                  <span className="font-semibold text-blue-900">Recent Trend</span>
                </div>
                <Badge className={`px-3 py-1 rounded-xl font-medium ${
                  analytics.recentTrend === 'increasing' ? 'bg-green-100 text-green-800' :
                  analytics.recentTrend === 'decreasing' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                }`}>
                  {analytics.recentTrend === 'increasing' ? '📈 Increasing' :
                   analytics.recentTrend === 'decreasing' ? '📉 Decreasing' : '📊 Stable'}
                </Badge>
              </div>

              {/* Hourly Distribution Preview */}
              <div className="p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl border border-purple-200">
                <h4 className="font-semibold text-purple-900 mb-3 flex items-center gap-2">
                  <PieChart className="h-4 w-4" />
                  Hourly Distribution
                </h4>
                <div className="space-y-2">
                  {analytics.hourlyDistribution.slice(0, 3).map(({ hour, count }) => (
                    <div key={hour} className="flex items-center justify-between">
                      <span className="text-purple-700">{hour}:00 - {hour + 1}:00</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 h-2 bg-purple-200 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-gradient-secondary rounded-full"
                            style={{ width: `${(count / analytics.totalAttendees) * 100}%` }}
                          />
                        </div>
                        <span className="text-purple-900 font-medium text-sm">{count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Session Performance */}
        <Card className="card-elevated border-0">
          <CardHeader className="pb-6">
            <CardTitle className="flex items-center gap-3 text-heading text-gray-800">
              <div className="w-10 h-10 bg-gradient-warm rounded-xl flex items-center justify-center">
                <Award className="h-5 w-5 text-white" />
              </div>
              Session Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Session Duration */}
              <div className="p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-xl border border-green-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-semibold text-green-900">Session Duration</span>
                  <Calendar className="h-4 w-4 text-green-600" />
                </div>
                <p className="text-green-700">
                  {Math.round((Date.now() - session.createdAt.getTime()) / (1000 * 60))} minutes active
                </p>
              </div>

              {/* Check-in Rate */}
              <div className="p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl border border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-semibold text-blue-900">Check-in Velocity</span>
                  <Activity className="h-4 w-4 text-blue-600" />
                </div>
                <p className="text-blue-700">
                  {(analytics.totalAttendees / Math.max(1, Math.round((Date.now() - session.createdAt.getTime()) / (1000 * 60)))).toFixed(1)} attendees/min
                </p>
              </div>

              {/* Completion Status */}
              <div className="p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl border border-purple-200">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-semibold text-purple-900">Session Status</span>
                  <Target className="h-4 w-4 text-purple-600" />
                </div>
                <Badge className="bg-green-100 text-green-800 px-3 py-1 rounded-xl font-medium">
                  🟢 Active & Collecting
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsReports;
